// Game state
const gameState = {
    playerMaxHealth: 100,
    playerHealth: 100,
    enemyMaxHealth: 100,
    enemyHealth: 100,
    currentQuestion: 0,
    timer: null,
    timeLeft: 45,
    isGameActive: false,
    correctAnswers: 0,
    totalQuestions: 0,
    powerUps: {
        addLife: { count: 1, active: false },
        protect: { count: 1, active: false },
        addTime: { count: 1, active: false }
    },
    isProtected: false,
    incorrectQuestions: [], // Store questions that were answered incorrectly
    isReviewingIncorrectQuestions: false // Flag to track if we're reviewing incorrect questions
};

// Store questions from database
let gameData = [];

// Sound Effects
const correctSound = new Audio('../../sounds/correct2.mp3');
const wrongSound = new Audio('../../sounds/wrong2.mp3');
const successSound = new Audio('../../sounds/success.mp3');
const deathSound = new Audio('../../sounds/break2.mp3');

// Set initial volume for all sound effects
correctSound.volume = 0.3;
wrongSound.volume = 0.3;
successSound.volume = 0.3;
deathSound.volume = 0.5;

// DOM Elements
const questionText = document.querySelector('.question-text');
const optionButtons = document.querySelectorAll('.option-btn');
const timerBar = document.querySelector('.timer-bar');
const timerText = document.querySelector('.timer-text');
const playerHealthBar = document.querySelector('.player-health');
const enemyHealthBar = document.querySelector('.enemy-health');
const playerHealthValue = document.querySelector('.player-side .health-value');
const enemyHealthValue = document.querySelector('.enemy-side .health-value');
const playerRobot = document.querySelector('.player-robot');
const enemyRobot = document.querySelector('.enemy-robot');
const instructionsModal = document.querySelector('.instructions-modal');
const resultsModal = document.querySelector('.results-modal');
const resultTitle = document.querySelector('.result-title');
const resultMessage = document.querySelector('.result-message');
const startButton = document.querySelector('.start-btn');
const replayButton = document.querySelector('.replay-btn');
const mainMenuButton = document.querySelector('.main-menu-btn');

// Power Up Elements
const powerUpButtons = {
    addLife: document.getElementById('add-life'),
    protect: document.getElementById('protect'),
    addTime: document.getElementById('add-time')
};
const powerUpCounts = {
    addLife: document.querySelector('#add-life .power-up-count'),
    protect: document.querySelector('#protect .power-up-count'),
    addTime: document.querySelector('#add-time .power-up-count')
};

// Initialize game
async function initGame() {
    try {
        // Reset game state including power-ups
        gameState.playerHealth = gameState.playerMaxHealth;
        gameState.enemyHealth = gameState.enemyMaxHealth;
        gameState.currentQuestion = 0;
        gameState.timeLeft = 45;
        gameState.isGameActive = false;
        gameState.correctAnswers = 0;
        gameState.isProtected = false;
        gameState.incorrectQuestions = [];
        gameState.isReviewingIncorrectQuestions = false;
        
        // Reset power-ups
        gameState.powerUps = {
            addLife: { count: 1, active: false },
            protect: { count: 1, active: false },
            addTime: { count: 1, active: false }
        };
        
        // Reset robot expressions and damage states
        playerRobot.classList.remove('lightly-damaged', 'damaged', 'critical', 'victory', 'defeat', 'hurt');
        enemyRobot.classList.remove('lightly-damaged', 'damaged', 'critical', 'victory', 'defeat', 'hurt');
        
        // Update UI
        updatePowerUpUI();
        updateHealthBars();
        
        // Get the level from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const level = parseInt(urlParams.get('level')) || 1; // Default to level 1 if not set

        console.log('Current URL:', window.location.href); // Debug log
        console.log('Level parameter from URL:', urlParams.get('level')); // Debug log
        console.log('Parsed level:', level); // Debug log

        // Fetch questions from the database
        const response = await fetch(`../../php/game.php?level=${level}`);
        const result = await response.json();
        

        console.log('Fetched data:', result); // Debug log

        if (result.success) {
            gameData = result.data;
            console.log('Game data loaded:', gameData); // Debug log

            if (gameData.length === 0) {
                console.error('No questions found in the database');
                alert('No questions found. Please try again later.');
                return;
            }

            // Initialize game state
            gameState.playerHealth = gameState.playerMaxHealth;
            gameState.enemyHealth = gameState.enemyMaxHealth;
            gameState.currentQuestion = 0;
            gameState.isGameActive = false;
            gameState.correctAnswers = 0;
            gameState.totalQuestions = gameData.length;

            updateHealthBars();
            loadQuestion();
            // Don't start timer here - wait for start button click

            // Reset timer display
            gameState.timeLeft = 45;
            timerText.textContent = gameState.timeLeft;
            timerBar.style.width = '100%';

            // Show instructions modal on first load and hide game container
            instructionsModal.style.display = 'flex';
            document.body.classList.add('modal-active');
        } else {
            console.error('Failed to load game data:', result.error);
            alert('Failed to load game data. Please try again later.');
        }
    } catch (error) {
        console.error('Error loading game data:', error);
        alert('Error loading game data. Please try again later.');
    }
}

// Load question
function loadQuestion() {
    // Check if we have questions loaded
    if (gameData.length === 0) {
        console.error('No questions available');
        return;
    }

    // Check if we've completed all questions
    if (gameState.currentQuestion >= gameData.length) {
        endGame(true); // Player wins if they complete all questions
        return;
    }

    // Make the game active for the new question
    gameState.isGameActive = true;

    const currentQ = gameData[gameState.currentQuestion];
    console.log('Current question:', currentQ); // Debug log

    if (!currentQ || !currentQ.question_text) {
        console.error('Invalid question data:', currentQ);
        return;
    }

    questionText.textContent = currentQ.question_text;

    // Create options from the database fields
    const options = [
        currentQ.option1,
        currentQ.option2,
        currentQ.option3,
        currentQ.option4
    ];

    // Shuffle options
    const shuffledOptions = shuffleArray([...options]);

    optionButtons.forEach((button, index) => {
        if (!shuffledOptions[index]) {
            console.error('Missing option at index:', index);
            return;
        }
        button.textContent = shuffledOptions[index];
        button.setAttribute('data-value', shuffledOptions[index]);
        button.disabled = false;
        button.classList.remove('correct', 'incorrect');
    });
}

// Start timer
function startTimer() {
    gameState.timeLeft = 45;
    timerText.textContent = gameState.timeLeft;
    timerBar.style.width = '100%';

    if (gameState.timer) {
        clearInterval(gameState.timer);
    }

    gameState.timer = setInterval(() => {
        gameState.timeLeft--;
        timerText.textContent = gameState.timeLeft;
        timerBar.style.width = `${(gameState.timeLeft / 45) * 100}%`;

        if (gameState.timeLeft <= 0) {
            clearInterval(gameState.timer);
            handleTimeout();
        }
    }, 1000);
}

// Update health bars
function updateHealthBars() {
    // Update visual health bars
    playerHealthBar.style.width = `${(gameState.playerHealth / gameState.playerMaxHealth) * 100}%`;
    enemyHealthBar.style.width = `${(gameState.enemyHealth / gameState.enemyMaxHealth) * 100}%`;

    // Update health text values
    playerHealthValue.textContent = `${gameState.playerHealth}/${gameState.playerMaxHealth}`;
    enemyHealthValue.textContent = `${gameState.enemyHealth}/${gameState.enemyMaxHealth}`;

    // Enhanced robot facial expressions and death effect
    updateRobotExpressions();

    // Check for game end conditions
    if (gameState.playerHealth <= 0) {
        endGame(false);
    } else if (gameState.enemyHealth <= 0) {
        endGame(true);
    }
}

// Enhanced robot facial expressions and death effect
function updateRobotExpressions() {
    // Health-based facial expressions and crying effect
    const updateFace = (robot, health, maxHealth) => {
        const percent = (health / maxHealth) * 100;
        robot.classList.remove('neutral-face', 'worried-face', 'sad-face', 'crying');
        if (percent > 50) {
            robot.classList.add('neutral-face');
        } else if (percent > 25) {
            robot.classList.add('worried-face', 'crying'); // Start crying below 50%
        } else {
            robot.classList.add('sad-face', 'crying'); // More pronounced crying below 25%
        }
    };
    updateFace(playerRobot, gameState.playerHealth, gameState.playerMaxHealth);
    updateFace(enemyRobot, gameState.enemyHealth, gameState.enemyMaxHealth);
}

// Floating damage display
function showDamage(targetRobot, damage, color = '#ff4444', isCrit = false) {
    const damageElem = document.createElement('div');
    damageElem.className = 'floating-damage';
    damageElem.textContent = (isCrit ? 'CRIT! ' : '') + `-${damage}`;
    const rect = targetRobot.getBoundingClientRect();
    damageElem.style.position = 'fixed';
    damageElem.style.left = (rect.left + rect.width / 2 - 20) + 'px';
    damageElem.style.top = (rect.top + rect.height / 3) + 'px';
    damageElem.style.fontSize = isCrit ? '2.4rem' : '2rem';
    damageElem.style.fontWeight = 'bold';
    damageElem.style.color = color;
    damageElem.style.textShadow = `0 0 8px #fff, 0 0 16px ${color}`;
    damageElem.style.opacity = '1';
    damageElem.style.transition = 'transform 0.8s cubic-bezier(0.4,2,0.2,1), opacity 0.8s cubic-bezier(0.4,2,0.2,1)';
    damageElem.style.zIndex = '10001';
    damageElem.style.userSelect = 'none';
    damageElem.style.transform = 'scale(1.4)'; // pop effect
    document.body.appendChild(damageElem);
    setTimeout(() => {
        damageElem.style.transform = 'translateY(-60px) scale(1)';
        damageElem.style.opacity = '0';
    }, 80);
    setTimeout(() => {
        damageElem.remove();
    }, 950);
}

// Helper to create and animate a laser beam
function showLaserBeam(fromRobot, toRobot, color) {
    // Get bounding rectangles
    const fromHead = fromRobot.querySelector('.robot-head');
    const toHead = toRobot.querySelector('.robot-head');
    const fromHeadRect = fromHead.getBoundingClientRect();
    const toHeadRect = toHead.getBoundingClientRect();
    
    // Calculate coordinates relative to viewport
    const startX = fromHeadRect.left + fromHeadRect.width / 2;
    const startY = fromHeadRect.top + fromHeadRect.height / 2;
    const endX = toHeadRect.left + toHeadRect.width / 2;
    const endY = toHeadRect.top + toHeadRect.height / 2;

    // Enhanced charging effect at the source
    createChargingEffect(fromRobot, color, () => {
        // Create the main laser beam
        createLaserBeam(startX, startY, endX, endY, color);
        // Create hit effect at target
        setTimeout(() => {
            createHitEffect(endX, endY, color);
        }, 350);
    });
}

// Enhanced createChargingEffect with callback and longer duration
function createChargingEffect(robot, color, onComplete) {
    const eyes = robot.querySelectorAll('.robot-eye');
    eyes.forEach(eye => {
        eye.style.boxShadow = `0 0 30px ${color}, 0 0 60px ${color}`;
        eye.style.background = color;
        eye.style.animation = 'charging-pulse 0.8s ease-in-out';
    });
    // Add charging text
    const chargingText = document.createElement('div');
    chargingText.textContent = 'CHARGING...';
    chargingText.style.position = 'fixed';
    const rect = robot.getBoundingClientRect();
    chargingText.style.left = (rect.left + rect.width / 2 - 60) + 'px';
    chargingText.style.top = (rect.top - 30) + 'px';
    chargingText.style.fontSize = '1.2rem';
    chargingText.style.fontWeight = 'bold';
    chargingText.style.color = color;
    chargingText.style.textShadow = '0 0 8px #fff, 0 0 16px ' + color;
    chargingText.style.zIndex = '10001';
    document.body.appendChild(chargingText);
    // Add more particles
    const particleContainer = document.createElement('div');
    particleContainer.style.position = 'fixed';
    particleContainer.style.left = '0';
    particleContainer.style.top = '0';
    particleContainer.style.width = '100vw';
    particleContainer.style.height = '100vh';
    particleContainer.style.pointerEvents = 'none';
    particleContainer.style.zIndex = '9998';
    document.body.appendChild(particleContainer);
    for (let i = 0; i < 16; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            const robotRect = robot.getBoundingClientRect();
            const centerX = robotRect.left + robotRect.width / 2;
            const centerY = robotRect.top + robotRect.height / 2;
            particle.style.position = 'fixed';
            particle.style.left = (centerX + (Math.random() - 0.5) * 120) + 'px';
            particle.style.top = (centerY + (Math.random() - 0.5) * 120) + 'px';
            particle.style.width = '5px';
            particle.style.height = '5px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.boxShadow = `0 0 14px ${color}`;
            particle.style.animation = 'particle-charge 0.8s ease-out forwards';
            particleContainer.appendChild(particle);
            setTimeout(() => particle.remove(), 800);
        }, i * 40);
    }
    // Remove charging effect after animation
    setTimeout(() => {
        eyes.forEach(eye => {
            eye.style.boxShadow = '';
            eye.style.background = '';
            eye.style.animation = '';
        });
        chargingText.remove();
        particleContainer.remove();
        if (onComplete) onComplete();
    }, 800);
}

// Create the main laser beam
function createLaserBeam(startX, startY, endX, endY, color) {
    // Create SVG container
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.style.position = 'fixed';
    svg.style.left = '0';
    svg.style.top = '0';
    svg.style.width = '100vw';
    svg.style.height = '100vh';
    svg.style.pointerEvents = 'none';
    svg.style.zIndex = '9999';
    svg.classList.add('laser-beam-svg');

    // Create main laser line
    const mainLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    mainLine.setAttribute('x1', startX);
    mainLine.setAttribute('y1', startY);
    mainLine.setAttribute('x2', startX);
    mainLine.setAttribute('y2', startY);
    mainLine.setAttribute('stroke', color);
    mainLine.setAttribute('stroke-width', '12');
    mainLine.setAttribute('stroke-linecap', 'round');
    mainLine.setAttribute('filter', 'drop-shadow(0 0 20px ' + color + ')');
    mainLine.style.opacity = '0';
    svg.appendChild(mainLine);

    // Create outer glow line
    const glowLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    glowLine.setAttribute('x1', startX);
    glowLine.setAttribute('y1', startY);
    glowLine.setAttribute('x2', startX);
    glowLine.setAttribute('y2', startY);
    glowLine.setAttribute('stroke', color);
    glowLine.setAttribute('stroke-width', '20');
    glowLine.setAttribute('stroke-linecap', 'round');
    glowLine.setAttribute('filter', 'blur(8px)');
    glowLine.style.opacity = '0.3';
    svg.appendChild(glowLine);

    document.body.appendChild(svg);

    // Animate laser firing
    setTimeout(() => {
        // Fade in
        mainLine.style.transition = 'opacity 0.1s';
        glowLine.style.transition = 'opacity 0.1s';
        mainLine.style.opacity = '1';
        glowLine.style.opacity = '0.3';

        // Extend to target
        setTimeout(() => {
            mainLine.setAttribute('x2', endX);
            mainLine.setAttribute('y2', endY);
            glowLine.setAttribute('x2', endX);
            glowLine.setAttribute('y2', endY);
        }, 50);
    }, 10);

    // Add laser trail particles
    createLaserTrail(startX, startY, endX, endY, color);

    // Fade out and remove laser
    setTimeout(() => {
        mainLine.style.transition = 'opacity 0.3s';
        glowLine.style.transition = 'opacity 0.3s';
        mainLine.style.opacity = '0';
        glowLine.style.opacity = '0';
        setTimeout(() => {
            svg.remove();
        }, 300);
    }, 400);
}

// Create laser trail particles
function createLaserTrail(startX, startY, endX, endY, color) {
    const trailContainer = document.createElement('div');
    trailContainer.style.position = 'fixed';
    trailContainer.style.left = '0';
    trailContainer.style.top = '0';
    trailContainer.style.width = '100vw';
    trailContainer.style.height = '100vh';
    trailContainer.style.pointerEvents = 'none';
    trailContainer.style.zIndex = '9997';
    document.body.appendChild(trailContainer);

    // Create trail particles along the laser path
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const progress = i / 14;
            const x = startX + (endX - startX) * progress;
            const y = startY + (endY - startY) * progress;
            
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.left = (x - 2) + 'px';
            particle.style.top = (y - 2) + 'px';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.boxShadow = `0 0 8px ${color}`;
            particle.style.animation = 'trail-fade 0.4s ease-out forwards';
            trailContainer.appendChild(particle);
            
            setTimeout(() => particle.remove(), 400);
        }, i * 20);
    }

    setTimeout(() => trailContainer.remove(), 600);
}

// Create hit effect at target
function createHitEffect(x, y, color) {
    // Create main hit burst
    const hit = document.createElement('div');
    hit.className = 'laser-hit-effect';
    hit.style.position = 'fixed';
    hit.style.left = (x - 30) + 'px';
    hit.style.top = (y - 30) + 'px';
    hit.style.width = '60px';
    hit.style.height = '60px';
    hit.style.borderRadius = '50%';
    hit.style.background = `radial-gradient(circle, ${color} 0%, rgba(255,255,255,0.8) 30%, transparent 70%)`;
    hit.style.boxShadow = `0 0 40px 15px ${color}`;
    hit.style.opacity = '0';
    hit.style.pointerEvents = 'none';
    hit.style.zIndex = '10000';
    hit.style.animation = 'laser-hit-burst 0.5s ease-out forwards';
    document.body.appendChild(hit);

    // Create shockwave rings
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const ring = document.createElement('div');
            ring.style.position = 'fixed';
            ring.style.left = (x - 20) + 'px';
            ring.style.top = (y - 20) + 'px';
            ring.style.width = '40px';
            ring.style.height = '40px';
            ring.style.border = `2px solid ${color}`;
            ring.style.borderRadius = '50%';
            ring.style.opacity = '0.8';
            ring.style.pointerEvents = 'none';
            ring.style.zIndex = '9999';
            ring.style.animation = 'shockwave 0.6s ease-out forwards';
            document.body.appendChild(ring);
            
            setTimeout(() => ring.remove(), 600);
        }, i * 100);
    }

    // Create impact particles
    for (let i = 0; i < 12; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            const angle = (i / 12) * Math.PI * 2;
            const distance = 40 + Math.random() * 20;
            const px = x + Math.cos(angle) * distance;
            const py = y + Math.sin(angle) * distance;
            
            // Set random direction for particle movement
            const dx = (Math.random() - 0.5) * 60;
            const dy = (Math.random() - 0.5) * 60;
            
            particle.style.position = 'fixed';
            particle.style.left = (px - 2) + 'px';
            particle.style.top = (py - 2) + 'px';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.boxShadow = `0 0 6px ${color}`;
            particle.style.setProperty('--dx', dx + 'px');
            particle.style.setProperty('--dy', dy + 'px');
            particle.style.animation = 'impact-particle 0.8s ease-out forwards';
            document.body.appendChild(particle);
            
            setTimeout(() => particle.remove(), 800);
        }, i * 30);
    }

    setTimeout(() => hit.remove(), 500);
}

// Handle player attack
function playerAttack() {
    if (!gameState.isGameActive) return;
    playerRobot.classList.add('attack-animation');
    showLaserBeam(playerRobot, enemyRobot, '#00F0FF');
    const damage = Math.ceil(100 / gameState.totalQuestions);
    setTimeout(() => {
        gameState.enemyHealth -= damage;
        if (gameState.enemyHealth < 0) gameState.enemyHealth = 0;
        showDamage(enemyRobot, damage, '#00F0FF', false);
        enemyRobot.classList.add('hurt');
        setTimeout(() => { enemyRobot.classList.remove('hurt'); }, 400);
        updateHealthBars();
        if (gameState.enemyHealth <= 0) endGame(true);
        playerRobot.classList.remove('attack-animation');
        loadNextQuestion();
    }, 1000);
}

// Handle enemy attack
function enemyAttack() {
    if (!gameState.isGameActive) return;
    enemyRobot.classList.add('attack-animation');
    showLaserBeam(enemyRobot, playerRobot, '#FF0055');
    let damage = 35;
    const isCritical = Math.random() < 0.2;
    if (isCritical) damage *= 2;
    setTimeout(() => {
        if (gameState.isProtected) {
            gameState.isProtected = false;
            updatePowerUpUI();
            const protectionEffect = document.createElement('div');
            protectionEffect.className = 'protection-effect';
            playerRobot.appendChild(protectionEffect);
            setTimeout(() => { protectionEffect.remove(); }, 1000);
            return;
        }
        gameState.playerHealth -= damage;
        if (gameState.playerHealth < 0) gameState.playerHealth = 0;
        showDamage(playerRobot, damage, '#FF0055', isCritical);
        playerRobot.classList.add('hurt');
        setTimeout(() => { playerRobot.classList.remove('hurt'); }, 400);
        updateHealthBars();
        if (gameState.playerHealth <= 0) endGame(false);
        enemyRobot.classList.remove('attack-animation');
        if (gameState.playerHealth > 0) loadNextQuestion();
    }, 1000);
}

// Load the next question or end the game if all questions are answered
function loadNextQuestion() {
    // Move to next question
    gameState.currentQuestion++;
    
    // Check if we've completed all questions
    if (gameState.currentQuestion >= gameData.length) {
        // Check if we have incorrect questions to review
        if (!gameState.isReviewingIncorrectQuestions && gameState.incorrectQuestions.length > 0) {
            // Switch to reviewing incorrect questions
            gameState.isReviewingIncorrectQuestions = true;
            
            // Replace gameData with incorrectQuestions for the review phase
            gameData = [...gameState.incorrectQuestions];
            gameState.incorrectQuestions = []; // Clear the array for potential new incorrect answers
            
            // Reset question index
            gameState.currentQuestion = 0;
            
            // Show a message to the user that they're now reviewing incorrect questions
            showReviewMessage();
            
            // Continue with the first incorrect question
            loadQuestion();
            startTimer();
        } else {
            // All questions answered correctly
            if (gameState.correctAnswers === gameState.totalQuestions) {
                endGame(true);
            } else {
                // If not all questions were answered correctly, it's a loss
                endGame(false);
            }
        }
    } else {
        // Load the next question and start the timer
        loadQuestion();
        startTimer();
    }
}

// Handle answer selection
function handleAnswer(selectedValue) {
    if (!gameState.isGameActive) return;
    optionButtons.forEach(btn => btn.disabled = true);
    clearInterval(gameState.timer);
    const currentQ = gameData[gameState.currentQuestion];
    const correctValue = currentQ.correct_answer;
    // Check if answer is correct
    if (selectedValue === correctValue) {
        // Highlight correct answer
        optionButtons.forEach(btn => {
            if (btn.getAttribute('data-value') === correctValue) {
                btn.classList.add('correct');
            }
        });
        gameState.correctAnswers++;
        playSound(correctSound);
        
        // Randomize exp between 10-20 for correct answer
        const expEarned = Math.floor(Math.random() * 11) + 10; // Random number between 10-20
        
        // Create and show EXP gain visual effect
        showExpGain(expEarned);
        
        fetch('../../php/savetodb.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                expEarned: expEarned,
                score: 0,
                starsCount: 0,
                level: parseInt(document.body.dataset.level)
            })
        })
        .then(response => response.json())
        .then(data => { if (!data.success) { console.error('Failed to save exp:', data.error); } })
        .catch(error => { console.error('Error saving exp:', error); });
        playerAttack();
    } else {
        // Highlight correct and incorrect answers
        optionButtons.forEach(btn => {
            if (btn.getAttribute('data-value') === selectedValue) {
                btn.classList.add('incorrect');
            }
            if (btn.getAttribute('data-value') === correctValue) {
                btn.classList.add('correct');
            }
        });
        playSound(wrongSound);
        enemyAttack();
        
        // Store the incorrect question to ask again later
        if (!gameState.isReviewingIncorrectQuestions) {
            // Make a deep copy of the current question to avoid reference issues
            const questionCopy = JSON.parse(JSON.stringify(currentQ));
            gameState.incorrectQuestions.push(questionCopy);
        }
        
        // No longer need to push back to gameData as we'll handle incorrect questions separately
        gameState.totalQuestions = gameData.length;
        setTimeout(() => {
            optionButtons.forEach(btn => {
                btn.disabled = false;
                btn.classList.remove('correct', 'incorrect');
            });
            startTimer();
        }, 1000);
    }
}

// Handle timeout (no answer selected)
function handleTimeout() {
    if (!gameState.isGameActive) return;
    optionButtons.forEach(btn => btn.disabled = true);
    const currentQ = gameData[gameState.currentQuestion];
    const correctValue = currentQ.correct_answer;
    optionButtons.forEach(btn => {
        if (btn.getAttribute('data-value') === correctValue) {
            btn.classList.add('correct');
        }
    });
    playSound(wrongSound);
    enemyAttack();
    setTimeout(() => {
        optionButtons.forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('correct', 'incorrect');
        });
        startTimer();
    }, 1000);
}

// Play Sound Effect
function playSound(sound) {
    // Reset the sound to the beginning
    sound.currentTime = 0;
    // Play the sound
    sound.play().catch(e => console.log('Audio play failed:', e));
}

// End game
function endGame(playerWon) {
    gameState.isGameActive = false;
    clearInterval(gameState.timer);
    // Add victory/defeat expressions
    if (playerWon) {
        playerRobot.classList.add('victory');
        enemyRobot.classList.add('defeat');
        // Dramatic death effect for enemy
        triggerDeathEffect(enemyRobot);
    } else {
        playerRobot.classList.add('defeat');
        enemyRobot.classList.add('victory');
        // Dramatic death effect for player
        triggerDeathEffect(playerRobot);
    }
    setTimeout(() => {
        showResults(playerWon);
    }, 1200);
}

// Show Results with Star Rating
function showResults(success) {
    const totalQuestions = gameState.totalQuestions;
    const correctAnswers = gameState.correctAnswers;
    const playerHealth = gameState.playerHealth;
    const totalExp = correctAnswers * 10; // 10 exp per correct answer

    // Calculate stars based on success (completing all questions and surviving)
    let stars;
    if (!success) {
        stars = 0;
    } else {
        // If player completed all questions and robot survived, award 3 stars
        stars = 3;
    }

    // Update modal content
    const resultTitle = document.getElementById('result-title');
    const resultMessage = document.getElementById('result-message');
    const resultStars = document.getElementById('result-stars');
    const expText = document.querySelector('.exp-text');

    if (success) {
        resultTitle.textContent = 'VICTORY!';
        resultMessage.textContent = `You defeated the enemy robot! Answered all ${totalQuestions} questions correctly with ${playerHealth} health remaining!`;

        // Add celebration animation and success sound
        if (playerRobot) {
            playerRobot.style.animation = 'victory-dance 2s ease-in-out infinite';
        }
        playSound(successSound);
    } else {
        resultTitle.textContent = 'DEFEAT!';
        if (playerHealth <= 0) {
            resultMessage.textContent = 'Your robot was destroyed! You need to answer all questions correctly to win!';
        } else {
            resultMessage.textContent = 'You ran out of questions! You need to answer all questions correctly to win!';
        }
    }

    // Display stars with enhanced animation
    if (resultStars) {
        resultStars.innerHTML = '';
        resultStars.classList.add('animate-in');

        // Add perfect score celebration for 3 stars
        if (stars === 3) {
            resultStars.classList.add('perfect-score');
        }

        for (let i = 0; i < 3; i++) {
            const star = document.createElement('span');
            star.textContent = i < stars ? '★' : '☆';
            star.style.color = i < stars ? '#ffeb3b' : '#555';
            resultStars.appendChild(star);
        }

        // Remove animation class after animation completes
        setTimeout(() => {
            resultStars.classList.remove('animate-in');
        }, 1200);
    }

    // Update exp display with animation
    if (expText) {
        expText.textContent = `+${totalExp} EXP`;
        const expContainer = document.querySelector('.exp-container');
        if (expContainer) {
            expContainer.classList.add('animate-in');

            // Add perfect score celebration for 3 stars
            if (stars === 3) {
                expContainer.classList.add('perfect-score');
            }

            // Remove animation class after animation completes
            setTimeout(() => {
                expContainer.classList.remove('animate-in');
            }, 1000);
        }
    }

    // Send final game data to savetodb.php
    fetch('../../php/savetodb.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            expEarned: 0, // We already sent exp per question
            score: correctAnswers,
            starsCount: stars,
            level: parseInt(document.body.dataset.level) // Get level from body data-level attribute
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to save final game data:', data.error);
        }
    })
    .catch(error => {
        console.error('Error saving final game data:', error);
    });

    // Show the modal
    resultsModal.style.display = 'flex';
}

// Shuffle array (Fisher-Yates algorithm)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Power Up Functions
function usePowerUp(type) {
    if (!gameState.isGameActive || gameState.powerUps[type].count <= 0) return;

    switch(type) {
        case 'addLife':
            gameState.playerHealth = Math.min(gameState.playerMaxHealth, gameState.playerHealth + 30);
            updateHealthBars();
            break;
            
        case 'protect':
            gameState.isProtected = true;
            break;
            
        case 'addTime':
            gameState.timeLeft += 10;
            timerText.textContent = gameState.timeLeft;
            timerBar.style.width = `${(gameState.timeLeft / 10) * 100}%`;
            break;
    }
    
    // Decrement power-up count and update UI
    gameState.powerUps[type].count--;
    updatePowerUpUI();
    
    // Visual feedback
    const button = powerUpButtons[type];
    button.classList.add('power-up-active');
    setTimeout(() => {
        button.classList.remove('power-up-active');
    }, 1000);
}

// Update power-up UI based on current state
function updatePowerUpUI() {
    for (const [type, data] of Object.entries(gameState.powerUps)) {
        const button = powerUpButtons[type];
        const countElement = powerUpCounts[type];
        
        // Update count display
        countElement.textContent = data.count;
        
        // Disable button if no more uses
        if (data.count <= 0) {
            button.disabled = true;
        } else {
            button.disabled = false;
        }
        
        // Update protect button appearance
        if (type === 'protect') {
            if (gameState.isProtected) {
                button.classList.add('power-up-active');
                button.title = 'Protected!';
            } else {
                button.classList.remove('power-up-active');
                button.title = 'Protect (Immune to next attack)';
            }
        }
    }
}

// Event Listeners
optionButtons.forEach((button) => {
    button.addEventListener('click', () => handleAnswer(button.getAttribute('data-value')));
});

// Add event listeners for power-up buttons
Object.entries(powerUpButtons).forEach(([type, button]) => {
    button.addEventListener('click', () => usePowerUp(type));
});

startButton.addEventListener('click', () => {
    instructionsModal.style.display = 'none';
    // Remove modal-active class to show game container
    document.body.classList.remove('modal-active');
    gameState.isGameActive = true;
    startTimer();
});

replayButton.addEventListener('click', () => {
    // Disable the button to prevent multiple clicks
    replayButton.disabled = true;
    replayButton.textContent = 'RESTARTING...';

    resultsModal.style.display = 'none';

    // Add a short delay before restarting the game
    setTimeout(() => {
        initGame();
        // Re-enable the button (will be reset when modal shows again)
        replayButton.disabled = false;
        replayButton.textContent = 'PLAY AGAIN';
    }, 800); // 800ms delay for better user experience
});

mainMenuButton.addEventListener('click', () => {
    window.location.href = '../../html/mainpage.html';
});

// Initialize the game when the page loads
window.addEventListener('load', initGame);

// Dramatic death effect (explosion/sparks/disintegration)
function triggerDeathEffect(robot) {
    // Play death sound
    playSound(deathSound);
    // Explosion effect
    const rect = robot.getBoundingClientRect();
    for (let i = 0; i < 18; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.left = (rect.left + rect.width / 2) + 'px';
            particle.style.top = (rect.top + rect.height / 2) + 'px';
            particle.style.width = '10px';
            particle.style.height = '10px';
            particle.style.background = `radial-gradient(circle, #ffeb3b 60%, #ff5722 100%)`;
            particle.style.borderRadius = '50%';
            particle.style.zIndex = '10002';
            particle.style.opacity = '0.9';
            const angle = Math.random() * 2 * Math.PI;
            const distance = 60 + Math.random() * 40;
            const dx = Math.cos(angle) * distance;
            const dy = Math.sin(angle) * distance;
            particle.style.transition = 'transform 0.7s cubic-bezier(0.4,2,0.2,1), opacity 0.7s';
            document.body.appendChild(particle);
            setTimeout(() => {
                particle.style.transform = `translate(${dx}px, ${dy}px) scale(0.7)`;
                particle.style.opacity = '0';
            }, 30);
            setTimeout(() => particle.remove(), 800);
        }, i * 30);
    }
    // Disintegration effect (fade out robot)
    robot.style.transition = 'opacity 0.7s cubic-bezier(0.4,2,0.2,1)';
    setTimeout(() => {
        robot.style.opacity = '0.2';
    }, 200);
    setTimeout(() => {
        robot.style.opacity = '';
        robot.style.transition = '';
    }, 1200);
}

// Show message when reviewing incorrect questions
function showReviewMessage() {
    const message = document.createElement('div');
    message.className = 'review-message';
    message.textContent = 'Now reviewing questions you answered incorrectly!';
    message.style.position = 'absolute';
    message.style.top = '10px';
    message.style.left = '50%';
    message.style.transform = 'translateX(-50%)';
    message.style.backgroundColor = 'rgba(255, 193, 7, 0.9)';
    message.style.color = '#333';
    message.style.padding = '10px 20px';
    message.style.borderRadius = '5px';
    message.style.zIndex = '1000';
    message.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    message.style.animation = 'fadeIn 0.5s ease-out';
    document.body.appendChild(message);
    
    // Remove the message after 3 seconds
    setTimeout(() => {
        message.style.animation = 'fadeOut 0.5s ease-in';
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 500);
    }, 3000);
}

// Add this new function to show EXP gain visual effect
function showExpGain(amount) {
    // Create EXP indicator element
    const expIndicator = document.createElement('div');
    expIndicator.className = 'exp-indicator';
    expIndicator.textContent = `+${amount} EXP`;
    
    // Style the indicator
    expIndicator.style.position = 'absolute';
    expIndicator.style.top = '50%';
    expIndicator.style.left = '50%';
    expIndicator.style.transform = 'translate(-50%, -50%)';
    expIndicator.style.color = '#00ffff'; // Cyan color to match robot theme
    expIndicator.style.fontSize = '28px';
    expIndicator.style.fontWeight = 'bold';
    expIndicator.style.textShadow = '0 0 10px rgba(0, 255, 255, 0.7)'; // Cyan glow
    expIndicator.style.zIndex = '1000';
    expIndicator.style.pointerEvents = 'none';
    expIndicator.style.opacity = '0';
    expIndicator.style.transition = 'all 1.5s ease-out';
    
    // Add to game container
    const gameContainer = document.querySelector('.game-container');
    gameContainer.appendChild(expIndicator);
    
    // Animate the indicator
    setTimeout(() => {
        expIndicator.style.opacity = '1';
        expIndicator.style.transform = 'translate(-50%, -100px)';
        expIndicator.style.fontSize = '36px';
        
        // Remove the indicator after animation
        setTimeout(() => {
            expIndicator.style.opacity = '0';
            setTimeout(() => {
                if (expIndicator.parentNode) {
                    expIndicator.parentNode.removeChild(expIndicator);
                }
            }, 500);
        }, 1000);
    }, 100);
}
