<?php
session_start();
header('Content-Type: application/json');

require_once 'dbconnection.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

try {
    $database = new Database();
    $pdo = $database->getConnection();

    $user_id = $_SESSION['userId'];

    // Get total number of levels from game_content
    $stmt = $pdo->prepare("SELECT COUNT(DISTINCT level_number) as total_levels FROM game_content");
    $stmt->execute();
    $total_levels = $stmt->fetch(PDO::FETCH_ASSOC)['total_levels'];

    // Get user's completed levels (levelStar > 0)
    $stmt = $pdo->prepare("SELECT COUNT(*) as completed_levels FROM user_levels WHERE user_id = ? AND levelStar > 0");
    $stmt->execute([$user_id]);
    $completed_levels = $stmt->fetch(PDO::FETCH_ASSOC)['completed_levels'];

    // Get total number of tiers
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_tiers FROM exp");
    $stmt->execute();
    $total_tiers = $stmt->fetch(PDO::FETCH_ASSOC)['total_tiers'];

    // Get user's current exp
    $stmt = $pdo->prepare("SELECT userExp FROM user_exp WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $user_exp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    $user_exp = $user_exp_data ? $user_exp_data['userExp'] : 0;

    // Get all exp tiers ordered by expNeeded to find the user's current tier
    $stmt = $pdo->prepare("SELECT expID, expName, expNeeded FROM exp ORDER BY expNeeded DESC");
    $stmt->execute();
    $exp_tiers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Find the highest tier the user qualifies for
    $current_tier_name = "Beginner"; // Default tier name
    $current_tier_id = 1; // Default tier ID
    $next_tier_exp = null;

    foreach ($exp_tiers as $tier) {
        if ($user_exp >= $tier['expNeeded']) {
            $current_tier_name = $tier['expName'];
            $current_tier_id = $tier['expID'];
            break;
        }
        $next_tier_exp = $tier['expNeeded']; // This will be the next tier requirement
    }

    // If no next tier found, user is at max level
    if ($next_tier_exp === null) {
        $next_tier_exp = $user_exp; // Set to current exp to show 100% progress
    }

    // Get unlocked achievements
    $stmt = $pdo->prepare("
        SELECT a.*, ua.unlocked_at 
        FROM achievements a 
        JOIN user_achievements ua ON a.id = ua.achievement_id 
        WHERE ua.user_id = ? AND ua.unlocked = 1
    ");
    $stmt->execute([$user_id]);
    $unlocked_achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate level progress percentage
    $level_progress = $total_levels > 0 ? ($completed_levels / $total_levels) * 100 : 0;

    // Calculate tier progress percentage based on experience
    // Find the current tier's exp requirement and next tier's exp requirement
    $current_tier_exp = 0;
    foreach ($exp_tiers as $tier) {
        if ($tier['expName'] === $current_tier_name) {
            $current_tier_exp = $tier['expNeeded'];
            break;
        }
    }

    // Calculate progress within current tier
    if ($next_tier_exp > $current_tier_exp) {
        $tier_progress = (($user_exp - $current_tier_exp) / ($next_tier_exp - $current_tier_exp)) * 100;
        $tier_progress = max(0, min(100, $tier_progress)); // Ensure it's between 0-100%
    } else {
        $tier_progress = 100; // Max level reached
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'levels' => [
                'completed' => $completed_levels,
                'total' => $total_levels,
                'progress' => $level_progress
            ],
            'tier' => [
                'current' => $current_tier_name,
                'current_id' => $current_tier_id,
                'total' => $total_tiers,
                'progress' => $tier_progress,
                'exp' => $user_exp,
                'next_tier_exp' => $next_tier_exp,
                'current_tier_exp' => $current_tier_exp
            ],
            'achievements' => [
                'unlocked' => $unlocked_achievements,
                'total' => count($unlocked_achievements)
            ]
        ]
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error',
        'error' => $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error',
        'error' => $e->getMessage()
    ]);
}
?>
