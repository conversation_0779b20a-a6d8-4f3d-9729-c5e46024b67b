<?php
header('Content-Type: application/json');

// Include the database connection
require_once 'dbconnection.php';
session_start();

$userId = $_SESSION['userId'] ?? null;

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Prepare and execute the query using PDO
    $stmt = $conn->prepare("SELECT username, email, bio, avatar FROM user_account WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    // Get the result
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo json_encode([
            'success' => true,
            'username' => $user['username'],
            'email' => $user['email'],
            'bio' => $user['bio'],
            'avatar' => $user['avatar']
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'User not found']);
    }
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
