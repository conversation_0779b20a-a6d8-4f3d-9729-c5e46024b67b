<?php
session_start();
header('Content-Type: application/json');

try {
    $pdo = new PDO('mysql:host=localhost;dbname=dbfunconnect', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if (!isset($_SESSION['userId'])) {
        throw new Exception('User not logged in');
    }

    $userId = $_SESSION['userId'];
    
    // Get unlocked levels and their stars
    $stmt = $pdo->prepare('SELECT level_number, levelStar FROM user_levels WHERE user_id = ? AND isUnlocked = 1');
    $stmt->execute([$userId]);
    $unlockedLevelsData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Count unlocked levels
    $unlockedLevelsCount = count($unlockedLevelsData);
    
    // Make sure to return at least level 1 as unlocked
    $unlockedLevelsCount = max(1, (int)$unlockedLevelsCount);
    
    // Prepare data to return
    $response = [
        'unlockedLevels' => $unlockedLevelsCount,
        'levelsData' => []
    ];
    
    // Add stars data for each level
    foreach ($unlockedLevelsData as $levelData) {
        $response['levelsData'][$levelData['level_number']] = (int)$levelData['levelStar'];
    }

    // Only output JSON - ensure no whitespace before <?php
    echo json_encode($response);
    exit;
    
} catch (Exception $e) {
    // Only output JSON
    echo json_encode(['error' => $e->getMessage()]);
    exit;
}