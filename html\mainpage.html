<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <link rel="stylesheet" href="../css/style_mainpage.css">
    <!-- Authentication Guard - Must be loaded before page content -->
    <script src="../js/auth-guard.js"></script>
</head>
<body>
    <!-- Add audio element for background music -->
    <audio id="background-music" loop autoplay muted preload="auto">
        <source src="../sounds/backgroung-music-8-bit-adventure.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <div id="toggle_profile" class="toggle-profile">
        <a href="javascript:void(0)" class="closebtn" onclick="toggle_close_profile()">&times;</a>

        <img id="profile-avatar" src="../images/profile-icon.png" alt="" srcset="">
        <p id="user-id-display">UID: Loading...</p>

        <a href="../html/profile.html">Edit Profile</a>
        <a href="../html/accounts.html">Account Settings</a>
        <a href="../html/progress.html">Progress</a>
        <a href="#" onclick="logout()">Logout</a>
    </div>

    <div class="header">
        <div class="box-cont1">
            <p>Fun Connect</p>

        </div>
        <div class="box-cont-exp">
            <p id="user_tier" class="user_exp">-</p>
            <p class="exp_label">EXP</p>  
        </div>

        <div class="box-cont2">
            <!-- Add sound toggle button -->
            <div class="box-cont2-bin">
                <img id="sound-toggle" src="../images/sound.png" alt="Toggle Sound" srcset="">
                <p>Sound</p>
            </div>
            
            <div class="box-cont2-bin">
                <img src="../images/Trophy2.png" onclick="window.location.href='../html/achievements.html'" alt="" srcset="">
                <p>Achievements</p>
            </div>
            
            <div class="box-cont2-bin">
               <img id="main-profile-icon" class="profile-icon" src="../images/profile-icon.png" onclick="toggle_open_profile()" alt="" srcset="">
                <p>Profile</p>
            </div>
          
        </div>
    </div>

    <div class="body">
        <div class="cont-levels">
            <div class="cont-levels-row">
                <div class="cont-levels-column">    
                    <button>
                        <a href="game/game1.html">1</a>
                    </button>
                </div>

                <div class="cont-levels-column">
                    <button>
                        <a href="game/robot_battle.html">2</a>
                    </button>
                </div>

                <div class="cont-levels-column">
                    <button>
                        <a href="game/game3.html">3</a>
                    </button>
                </div>
                
            </div>
        </div>
    </div>

   <script src="../js/mainpage.js"></script>
</body>
</html>