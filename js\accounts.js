document.addEventListener('DOMContentLoaded', function() {
    loadAccountData();

    const form = document.getElementById('accounts-form');
    form.addEventListener('submit', handleFormSubmit);

    // Modal elements
    const modal = document.getElementById('password-modal');
    const showPasswordBtn = document.getElementById('show-password-btn');
    const closeBtn = document.querySelector('.close-btn');
    const submitModalPasswordBtn = document.getElementById('submit-modal-password');

    showPasswordBtn.addEventListener('click', () => {
        modal.style.display = 'block';
    });

    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
        document.getElementById('modal-error').textContent = '';
    });

    window.addEventListener('click', (event) => {
        if (event.target == modal) {
            modal.style.display = 'none';
            document.getElementById('modal-error').textContent = '';
        }
    });

    submitModalPasswordBtn.addEventListener('click', verifyPasswordAndShow);
});

function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = {
        email: document.getElementById('email').value
    };
    
    saveAccountChanges(formData);
}

function saveAccountChanges(formData) {
    fetch('../php/save_account.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
    })
    .then(handleResponse)
    .then(data => {
        if (data.success) {
            alert('Account settings saved successfully!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving account settings: ' + error.message);
    });
}

function verifyPasswordAndShow() {
    const enteredPassword = document.getElementById('modal-password-input').value;
    const modalError = document.getElementById('modal-error');

    fetch('../php/get_account.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: enteredPassword })
    })
    .then(handleResponse)
    .then(data => {
        if (data.success) {
            const passwordInput = document.getElementById('password');
            passwordInput.type = 'text';
            passwordInput.value = data.password || enteredPassword;
            document.getElementById('password-modal').style.display = 'none';
            modalError.textContent = '';
        } else {
            modalError.textContent = data.message || 'Incorrect password. Please try again.';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        modalError.textContent = 'An error occurred. Please try again.';
    });
}

function loadAccountData() {
    fetch('../php/get_account.php')
    .then(handleResponse)
    .then(data => {
        if (data.success) {
            document.getElementById('username').value = data.username || '';
            document.getElementById('email').value = data.email || '';
            document.getElementById('password').value = '************';
        } else {
            console.error('Error loading account data:', data.message);
        }
    })
    .catch(error => {
        console.error('Error loading account data:', error);
    });
}

function handleResponse(response) {
    if (!response.ok) {
        return response.text().then(text => {
            throw new Error(text || 'Network response was not ok');
        });
    }
    return response.json().catch(() => {
        throw new Error('Invalid JSON response');
    });
}
