async function fetchAchievements() {
    try {
        const response = await fetch('../php/achievements.php', {
            credentials: 'include',
            headers: {
                'Accept': 'application/json'
            }
        });
        
        // First check if response is OK
        if (!response.ok) {
            // Try to get error message from response
            let errorMsg = 'Failed to fetch achievements';
            try {
                const errorData = await response.json();
                errorMsg = errorData.message || errorMsg;
            } catch (e) {
                errorMsg = `${response.status} ${response.statusText}`;
            }
            throw new Error(errorMsg);
        }
        
        // Get the response text first for debugging
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        // Then try to parse it
        try {
            return JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON:', e);
            throw new Error('Invalid server response format');
        }
        
    } catch (error) {
        console.error('Fetch error:', error);
        showErrorToUser(error.message);
        return {
            success: false,
            achievements: [],
            error: error.message
        };
    }
}

async function displayAchievements() {
    const container = document.querySelector('.body');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading achievements...</div>';
    
    try {
        // First fetch achievements data
        const achievementsData = await fetchAchievements();
        console.log('Parsed achievements data:', achievementsData);
            
        if (!achievementsData.success) {
            showErrorToUser(achievementsData.error || 'Failed to load achievements');
            return;
        }
        
        if (!achievementsData.achievements || !Array.isArray(achievementsData.achievements)) {
            throw new Error('Invalid achievements data');
        }

        // Then fetch progress data
        const progressResponse = await fetch('../php/update_achievements_progress.php', {
            credentials: 'include',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!progressResponse.ok) {
            throw new Error('Failed to fetch achievement progress');
        }

        const progressData = await progressResponse.json();
        console.log('Progress data:', progressData);

        // Create a map of achievement progress
        const progressMap = {};
        if (progressData.userLevels) {
            progressData.userLevels.forEach(level => {
                progressMap[level.level_number] = {
                    levelStar: level.levelStar || 0,
                    levelScore: level.levelScore || 0
                };
            });
        }
        
        container.innerHTML = '';
        
        achievementsData.achievements.forEach(achievement => {
            const cardContainer = document.createElement('div');
            cardContainer.className = 'card-container';
            
            const card = document.createElement('div');
            card.className = `card ${achievement.is_unlocked ? 'unlocked' : 'locked'}`;
            
            // Create achievement image
            const img = document.createElement('img');
            img.src = `${achievement.badge_image}`;
            img.alt = achievement.name;
            if (!achievement.is_unlocked) {
                img.classList.add('grayscale');
            }
            
            // Create achievement title
            const title = document.createElement('p');
            title.className = 'achievement-title';
            title.textContent = achievement.name;
            
            // Create achievement description
            const description = document.createElement('p');
            description.className = 'achievement-description';
            description.textContent = achievement.description;
            
            // Calculate progress based on condition type
            let progress = 0;
            let total = 0;
            
            switch (achievement.condition_type) {
                case 'game completed':
                    // Count completed levels (levelStar > 0)
                    progress = Object.values(progressMap).filter(level => level.levelStar > 0).length;
                    total = achievement.condition_value;
                    break;
                    
                case 'star earned':
                    // Sum all stars earned
                    progress = Object.values(progressMap).reduce((sum, level) => sum + (level.levelStar || 0), 0);
                    total = achievement.condition_value;
                    break;
                    
                case 'star streak 2':
                    // Find longest streak of 2 stars
                    let currentStreak = 0;
                    let maxStreak = 0;
                    Object.values(progressMap).forEach(level => {
                        if (level.levelStar >= 2) {
                            currentStreak++;
                            maxStreak = Math.max(maxStreak, currentStreak);
                        } else {
                            currentStreak = 0;
                        }
                    });
                    progress = maxStreak;
                    total = achievement.condition_value;
                    break;
            }
            
            // Create progress bar
            const progressContainer = document.createElement('div');
            progressContainer.className = 'progress-container';
            
            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            
            const progressFill = document.createElement('div');
            progressFill.className = 'progress-fill';
            const percentage = Math.min((progress / total) * 100, 100);
            progressFill.style.width = `${percentage}%`;
            
            const progressText = document.createElement('span');
            progressText.className = 'progress-text';
            progressText.textContent = `${progress}/${total}`;
            
            progressBar.appendChild(progressFill);
            progressContainer.appendChild(progressBar);
            progressContainer.appendChild(progressText);
            
            // Add lock overlay for locked achievements
            if (!achievement.is_unlocked) {
                const lockOverlay = document.createElement('div');
                lockOverlay.className = 'lock-overlay';
                lockOverlay.innerHTML = '🔒';
                card.appendChild(lockOverlay);
            }
            
            // Assemble the card
            card.appendChild(img);
            card.appendChild(title);
            card.appendChild(description);
            card.appendChild(progressContainer);
            
            cardContainer.appendChild(card);
            container.appendChild(cardContainer);
        });
        
    } catch (error) {
        console.error('Display error:', error);
        container.innerHTML = `
            <div class="error">
                ${error.message || 'Error loading achievements'}
                <button onclick="displayAchievements()">Retry</button>
            </div>
        `;
    }
}


// Helper function to show errors to user
function showErrorToUser(message) {
    const errorContainer = document.getElementById('error-container') || createErrorContainer();
    errorContainer.textContent = message;
    errorContainer.style.display = 'block';
    
    setTimeout(() => {
        errorContainer.style.display = 'none';
    }, 5000);
}

function createErrorContainer() {
    const container = document.createElement('div');
    container.id = 'error-container';
    container.style = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px;
        background: #ffebee;
        color: #c62828;
        border: 1px solid #ef9a9a;
        border-radius: 4px;
        display: none;
        z-index: 1000;
    `;
    document.body.appendChild(container);
    return container;
}

// Call the function when the page loads
document.addEventListener('DOMContentLoaded', displayAchievements);