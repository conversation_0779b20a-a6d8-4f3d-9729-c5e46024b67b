<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Progress</title>
    <link rel="stylesheet" href="../css/style_progress.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap" rel="stylesheet">
    <!-- Authentication Guard - Must be loaded before page content -->
    <script src="../js/auth-guard.js"></script>
</head>
<body>
    <div class="container">
        <a href="mainpage.html" class="x-button">
            <img src="../images/x.svg" alt="Close">
        </a>
        
        <div class="header">
            <h1>Your Progress</h1>
        </div>

        <div class="progress-section">
            <h2>Level Progress</h2>
            <div class="progress-bar">
                <div id="level-progress" class="progress-fill"></div>
            </div>
            <p id="level-text" class="progress-text">0/0 Levels</p>
        </div>

        <div class="progress-section">
            <h2>Experience Progress</h2>
            <div class="progress-bar">
                <div id="tier-progress" class="progress-fill"></div>
            </div>
            <p id="tier-text" class="progress-text">Experience 0/0 (0 EXP)</p>
        </div>

        <div class="achievements-section">
            <h2>Achievements</h2>
            <p id="achievements-count" class="achievements-count">0 Achievements Unlocked</p>
            <div id="achievements-container" class="achievements-container">
                <!-- Achievement cards will be inserted here -->
            </div>
        </div>
    </div>

    <script src="../js/progress.js"></script>
</body>
</html> 