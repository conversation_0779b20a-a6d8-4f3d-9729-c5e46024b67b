<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set header to JSON
header('Content-Type: application/json');

// Start session and include database connection
session_start();

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

try {
    // Check if database connection file exists
    if (!file_exists('dbconnection.php')) {
        throw new Exception('Database connection file not found at: ' . __DIR__ . '/dbconnection.php');
    }

    require_once 'dbconnection.php';

    // Initialize database connection
    $database = new Database();
    $pdo = $database->getConnection();

    // Check if database connection is established
    if (!$pdo) {
        throw new Exception('Database connection failed - PDO object not properly initialized');
    }

    $user_id = $_SESSION['userId'];

    // Get all achievements
    $stmt = $pdo->prepare("SELECT * FROM achievements");
    if (!$stmt) {
        throw new Exception('Failed to prepare achievements query');
    }
    $stmt->execute();
    $achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user's levels data
    $stmt = $pdo->prepare("SELECT * FROM user_levels WHERE user_id = ?");
    if (!$stmt) {
        throw new Exception('Failed to prepare user levels query');
    }
    $stmt->execute([$user_id]);
    $userLevels = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add userLevels to the response
    $response = [
        'success' => true,
        'message' => 'Achievement progress updated successfully',
        'userLevels' => $userLevels,
        'debug_info' => []
    ];

    // Process each achievement
    foreach ($achievements as $achievement) {
        $progress = 0;
        $condition_type = $achievement['condition_type'];
        $condition_value = $achievement['condition_value'];

        // Debug info for achievement_id 1
        if ($achievement['id'] == 1) {
            $debug_info = [
                'achievement_id' => $achievement['id'],
                'condition_type' => $condition_type,
                'condition_value' => $condition_value,
                'userLevels' => $userLevels,
                'level_stars' => array_column($userLevels, 'levelStar'),
                'completed_levels' => array_filter($userLevels, function($level) {
                    return $level['levelStar'] > 0;
                })
            ];
        }

        switch ($condition_type) {
            case 'game completed':
                // Count completed levels (levelStar > 0)
                $completed_levels = array_filter($userLevels, function($level) {
                    return $level['levelStar'] > 0;
                });
                $progress = count($completed_levels);
                break;

            case 'star earned':
                // Sum all stars earned
                $progress = array_sum(array_column($userLevels, 'levelStar'));
                break;

            case 'star streak 2':
                // Find longest streak of 2 stars
                $currentStreak = 0;
                $maxStreak = 0;
                
                foreach ($userLevels as $level) {
                    if ($level['levelStar'] >= 2) {
                        $currentStreak++;
                        $maxStreak = max($maxStreak, $currentStreak);
                    } else {
                        $currentStreak = 0;
                    }
                }
                $progress = $maxStreak;
                break;
        }

        // Check if achievement is already unlocked
        $stmt = $pdo->prepare("SELECT unlocked, progress FROM user_achievements WHERE user_id = ? AND achievement_id = ?");
        if (!$stmt) {
            throw new Exception('Failed to prepare achievement check query');
        }
        $stmt->execute([$user_id, $achievement['id']]);
        $existingAchievement = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingAchievement) {
            // Always update progress
            $stmt = $pdo->prepare("UPDATE user_achievements SET progress = ? WHERE user_id = ? AND achievement_id = ?");
            if (!$stmt) {
                throw new Exception('Failed to prepare progress update query');
            }
            $stmt->execute([$progress, $user_id, $achievement['id']]);

            // Check if achievement should be unlocked
            if (!$existingAchievement['unlocked'] && $progress >= $condition_value) {
                $stmt = $pdo->prepare("UPDATE user_achievements SET unlocked = 1, unlocked_at = NOW() WHERE user_id = ? AND achievement_id = ?");
                if (!$stmt) {
                    throw new Exception('Failed to prepare unlock update query');
                }
                $stmt->execute([$user_id, $achievement['id']]);
            }
        } else {
            // Insert new achievement progress
            $unlocked = ($progress >= $condition_value) ? 1 : 0;
            $unlocked_at = $unlocked ? date('Y-m-d H:i:s') : null;
            
            $stmt = $pdo->prepare("INSERT INTO user_achievements (user_id, achievement_id, progress, unlocked, unlocked_at) VALUES (?, ?, ?, ?, ?)");
            if (!$stmt) {
                throw new Exception('Failed to prepare achievement insert query');
            }
            $stmt->execute([$user_id, $achievement['id'], $progress, $unlocked, $unlocked_at]);
        }
    }

    echo json_encode($response);

} catch (PDOException $e) {
    http_response_code(500);
    error_log("Database Error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error',
        'error' => $e->getMessage(),
        'code' => $e->getCode()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    error_log("General Error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
