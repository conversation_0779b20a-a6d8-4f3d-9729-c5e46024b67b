<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Content Management</title>
    <link rel="stylesheet" href="../../css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="user-management.html">
                        <i class="fas fa-users"></i> Users Management
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="#">
                        <i class="fas fa-gamepad"></i> Game Content
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Game Content Management Section -->
            <section id="content-section" class="content-section active">
                <div class="section-header">
                    <h1>Game Content Management</h1>
                    <div class="search-bar">
                        <input type="text" id="levelSearch" placeholder="Search by level number..." onkeyup="searchLevels()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="content-controls">
                    <div class="content-info">
                        <span class="info-text">Manage game levels and their questions</span>
                    </div>
                    <div class="level-controls">
                        <button class="btn-secondary" id="toggleAllLevels" onclick="toggleAllLevels()">
                            <i class="fas fa-expand-alt"></i> Expand All
                        </button>
                    </div>
                </div>
                <!-- Game Levels List -->
                <div class="levels-container" id="levelsContainer">
                    <!-- Levels will be populated by JavaScript -->
                </div>
            </section>
        </main>
    </div>

    <!-- Question Modal -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="questionModalTitle">Add Question</h2>
                <span class="close" onclick="closeModal('questionModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="questionForm">
                    <div class="form-group">
                        <label for="quizType">Quiz Type:</label>
                        <select id="quizType" required>
                            <option value="Multiple Choice">Multiple Choice</option>
                            <option value="Matching Type">Matching Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="questionText">Question:</label>
                        <textarea id="questionText" required></textarea>
                    </div>
                    <div class="form-group option-field">
                        <label for="option1">Option A:</label>
                        <input type="text" id="option1">
                    </div>
                    <div class="form-group option-field">
                        <label for="option2">Option B:</label>
                        <input type="text" id="option2">
                    </div>
                    <div class="form-group option-field">
                        <label for="option3">Option C:</label>
                        <input type="text" id="option3">
                    </div>
                    <div class="form-group option-field">
                        <label for="option4">Option D:</label>
                        <input type="text" id="option4">
                    </div>
                    <div class="form-group">
                        <label for="correctAnswer">Correct Answer:</label>
                        <input type="text" id="correctAnswer" required placeholder="Type the exact answer here">
                        <small class="info-text" style="color:#888;display:block;margin-top:4px;">The correct answer must exactly match one of the options above (case-sensitive).</small>
                    </div>
                    <div class="form-group">
                        <label for="levelNumber">Level Number:</label>
                        <input type="number" id="levelNumber" min="1" required>
                    </div>
                    <input type="hidden" id="contentId" value="">
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('questionModal')">Cancel</button>
                        <button type="submit" class="btn-primary">OK</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/admin/game-management.js"></script>
</body>
</html>
