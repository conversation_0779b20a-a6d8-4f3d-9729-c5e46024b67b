<?php
require_once 'dbconnection.php';

// Set proper headers first
header('Content-Type: application/json');

session_start();
$user_id = $_SESSION['userId'] ?? null;

if (!$user_id) {
    http_response_code(401);
    die(json_encode(['success' => false, 'error' => 'User not authenticated']));
}

try {
    // Initialize database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Fetch all achievements with unlock status
    $query = "SELECT a.id, a.name, a.description, a.condition_type, a.condition_value, a.badge_image,
              IFNULL(ua.unlocked, 0) as is_unlocked
              FROM achievements a
              LEFT JOIN user_achievements ua ON a.id = ua.achievement_id AND ua.user_id = ?
              ORDER BY a.condition_type";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$user_id]);
    
    $achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add progress information for each achievement
    foreach ($achievements as &$achievement) {
        $progress = 0;
        $total = 0;
        
        switch ($achievement['condition_type']) {
            case 'levels_completed':
                // Count completed levels
                $stmt = $conn->prepare("SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND isUnlocked = 1");
                $stmt->execute([$user_id]);
                $progress = $stmt->fetchColumn();
                $total = $achievement['condition_value'];
                break;
                
            case 'total_score':
                // Get total score
                $stmt = $conn->prepare("SELECT SUM(levelScore) FROM user_levels WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $progress = $stmt->fetchColumn() ?? 0;
                $total = $achievement['condition_value'];
                break;
                
            case 'perfect_levels':
                // Count perfect levels (3 stars)
                $stmt = $conn->prepare("SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND levelStar = 3");
                $stmt->execute([$user_id]);
                $progress = $stmt->fetchColumn();
                $total = $achievement['condition_value'];
                break;
        }
        
        $achievement['progress'] = $progress;
        $achievement['total'] = $total;
        $achievement['percentage'] = $total > 0 ? round(($progress / $total) * 100) : 0;
    }

    echo json_encode([
        'success' => true,
        'achievements' => $achievements
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>