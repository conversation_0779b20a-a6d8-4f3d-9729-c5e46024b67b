<?php
require_once 'dbconnection.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'];
    $password = $_POST['password'];

    try {
        // Initialize database connection
        $database = new Database();
        $conn = $database->getConnection();

        // Check if username already exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM user_account WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->fetchColumn() > 0) {
            // Username already exists
            header("Location: ../html/signup.html?error=username_exists");
            exit;
        }

        // Function to get all existing level IDs from database
        function getAllLevelIds($conn) {
            $stmt = $conn->query("SELECT DISTINCT level_number FROM game_content ORDER BY level_number");
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        }

        // Function to generate a unique user ID
        function generateUniqueUserId($conn) {
            $maxAttempts = 100;
            $attempts = 0;
            
            while ($attempts < $maxAttempts) {
                $randomNumber = mt_rand(1, 1000000);
                
                $stmt = $conn->prepare("SELECT COUNT(*) FROM user_account WHERE user_id = ?");
                $stmt->execute([$randomNumber]);
                
                if ($stmt->fetchColumn() == 0) {
                    return $randomNumber;
                }
                
                $attempts++;
            }
            
            throw new Exception("Could not generate a unique user ID after $maxAttempts attempts");
        }

        // Get existing levels and generate user ID
        $existingLevels = getAllLevelIds($conn);
        $uniqueUserId = generateUniqueUserId($conn);
        $minLevel = 1;
        $maxLevel = count($existingLevels);

        // Start transaction
        $conn->beginTransaction();

        try {
            // Insert first level as unlocked
            $stmt = $conn->prepare("INSERT INTO user_levels (user_id, level_number, isUnlocked) VALUES (?, ?, 1)");
            $stmt->execute([$uniqueUserId, $existingLevels[0]]);

            // Insert remaining levels as locked
            for ($i = $minLevel; $i < $maxLevel; $i++) {
                $stmt = $conn->prepare("INSERT INTO user_levels (user_id, level_number) VALUES (?, ?)");
                $stmt->execute([$uniqueUserId, $existingLevels[$i]]);
            }

            // Insert user account
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO user_account (user_id, username, password) VALUES (?, ?, ?)");
            $stmt->execute([$uniqueUserId, $username, $hashedPassword]);

            // Insert user tier
            $stmt = $conn->prepare("INSERT INTO user_exp (user_id, expID, userExp) VALUES (?, 0, 0)");
            $stmt->execute([$uniqueUserId]);

            // Insert user achievements
            $stmt = $conn->prepare("INSERT INTO user_achievements (user_id, achievement_id, progress, unlocked) 
                                  SELECT ?, id, 0, 0 FROM achievements");
            $stmt->execute([$uniqueUserId]);

            // Commit transaction
            $conn->commit();
            header("Location: ../html/login.html?success=account_created");
            exit;

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            throw $e;
        }

    } catch (Exception $e) {
        header("Location: ../html/signup.html?error=db_error");
        exit;
    }
}