<?php
// Ensure no output before headers
ob_start();
header('Content-Type: application/json');

// Error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require_once 'dbconnection.php';

session_start();

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['userId'];

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON input');
    }

    // Validate required fields
    if (!isset($input['email'])) {
        throw new Exception('Missing required fields');
    }

    // Validate and sanitize input
    $email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
    $currentPassword = isset($input['currentPassword']) ? $input['currentPassword'] : '';
    $newPassword = isset($input['newPassword']) ? $input['newPassword'] : '';

    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }

    // If password change is requested
    if (!empty($newPassword)) {
        if (empty($currentPassword)) {
            throw new Exception('Current password is required to change password');
        }

        // Verify current password
        $stmt = $conn->prepare("SELECT password FROM user_account WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('User not found');
        }

        // Verify current password (assuming passwords are hashed)
        if (!password_verify($currentPassword, $user['password'])) {
            throw new Exception('Current password is incorrect');
        }

        // Hash new password
        $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        // Update email and password
        $stmt = $conn->prepare("UPDATE user_account SET email = :email, password = :password WHERE user_id = :user_id");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':password', $hashedNewPassword, PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    } else {
        // Update only email
        $stmt = $conn->prepare("UPDATE user_account SET email = :email WHERE user_id = :user_id");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    }

    // Execute and respond
    if ($stmt->execute()) {
        $response = ['success' => true, 'message' => 'Account settings saved successfully'];
    } else {
        throw new Exception('Failed to save account settings');
    }
    
} catch (PDOException $e) {
    $response = ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
} catch (Exception $e) {
    $response = ['success' => false, 'message' => $e->getMessage()];
}

echo json_encode($response);
ob_end_flush();
?>
