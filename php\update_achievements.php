<?php
require_once 'dbconnection.php';

// Set headers for JSON response
header('Content-Type: application/json');

session_start();
$user_id = $_SESSION['userId'] ?? null;

if (!$user_id) {
    http_response_code(401);
    die(json_encode(['success' => false, 'error' => 'User not authenticated']));
}

// Initialize response array
$response = [
    'success' => false,
    'updated_achievements' => [],
    'debug' => []
];

try {
    // Initialize database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Begin transaction
    $conn->beginTransaction();

    // 1. Get all achievements
    $achievements_query = "SELECT id, name, description, condition_type, condition_value, badge_image 
                          FROM achievements";
    $stmt = $conn->prepare($achievements_query);
    $stmt->execute();
    $achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $updated_achievements = [];
    
    foreach ($achievements as $achievement) {
        $achievement_id = $achievement['id'];
        $condition_type = $achievement['condition_type'];
        $condition_value = $achievement['condition_value'];
        
        // Debug info for this achievement
        $debug_info = [
            'achievement_id' => $achievement_id,
            'condition_type' => $condition_type,
            'condition_value' => $condition_value
        ];
        
        // Check if achievement is already unlocked
        $check_query = "SELECT unlocked FROM user_achievements WHERE user_id = ? AND achievement_id = ?";
        $stmt = $conn->prepare($check_query);
        $stmt->execute([$user_id, $achievement_id]);
        $is_unlocked = $stmt->fetchColumn();
        
        if ($is_unlocked) {
            continue; // Skip if already unlocked
        }
        
        $should_unlock = false;
        
        // Check different achievement conditions
        switch ($condition_type) {
            case 'game completed':
                // Count how many levels are unlocked
                $levels_query = "SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND isUnlocked = 1";
                $stmt = $conn->prepare($levels_query);
                $stmt->execute([$user_id]);
                $unlocked_levels = $stmt->fetchColumn();
                
                if ($unlocked_levels >= $condition_value) {
                    $should_unlock = true;
                }
                break;
                
            case 'star earned':
                // Count total stars earned across all levels
                $stars_query = "SELECT SUM(levelStar) FROM user_levels WHERE user_id = ?";
                $stmt = $conn->prepare($stars_query);
                $stmt->execute([$user_id]);
                $total_stars = $stmt->fetchColumn();
                
                if ($total_stars >= $condition_value) {
                    $should_unlock = true;
                }
                break;
                
            case 'star streak 2':
                // Count how many times user got 2 stars in a row
                $streak_query = "SELECT levelStar FROM user_levels WHERE user_id = ? ORDER BY level_number";
                $stmt = $conn->prepare($streak_query);
                $stmt->execute([$user_id]);
                $stars = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $streak_count = 0;
                $current_streak = 0;
                
                foreach ($stars as $star) {
                    if ($star == 2) {
                        $current_streak++;
                        if ($current_streak == 2) {
                            $streak_count++;
                            $current_streak = 0;
                        }
                    } else {
                        $current_streak = 0;
                    }
                }
                
                if ($streak_count >= $condition_value) {
                    $should_unlock = true;
                }
                break;
                
            case 'total_score':
                // Get user's total score
                $score_query = "SELECT SUM(levelScore) FROM user_levels WHERE user_id = ?";
                $stmt = $conn->prepare($score_query);
                $stmt->execute([$user_id]);
                $total_score = $stmt->fetchColumn();
                
                if ($total_score >= $condition_value) {
                    $should_unlock = true;
                }
                break;
                
            case 'perfect_levels':
                // Count perfect levels (3 stars)
                $perfect_query = "SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND levelStar = 3";
                $stmt = $conn->prepare($perfect_query);
                $stmt->execute([$user_id]);
                $perfect_levels = $stmt->fetchColumn();
                
                if ($perfect_levels >= $condition_value) {
                    $should_unlock = true;
                }
                break;
        }
        
        if ($should_unlock) {
            // Update achievement as unlocked
            $update_query = "UPDATE user_achievements SET unlocked = 1 WHERE user_id = ? AND achievement_id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->execute([$user_id, $achievement_id]);
                
            $updated_achievements[] = [
                'id' => $achievement_id,
                'name' => $achievement['name'],
                'description' => $achievement['description'],
                'badge_image' => $achievement['badge_image']
            ];
        }
    }

    // Commit transaction
    $conn->commit();

    $response['success'] = true;
    $response['updated_achievements'] = $updated_achievements;

} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    $response['error'] = $e->getMessage();
}

    echo json_encode($response);
?>