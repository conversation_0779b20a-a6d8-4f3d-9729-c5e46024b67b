<?php
header('Content-Type: application/json');

require_once 'dbconnection.php';
session_start();

$userId = $_SESSION['userId'] ?? null;
if (!$userId) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$database = new Database();
$conn = $database->getConnection();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle password verification for showing password
    $data = json_decode(file_get_contents('php://input'), true);
    $enteredPassword = $data['password'] ?? null;

    if (!$enteredPassword) {
        echo json_encode(['success' => false, 'message' => 'Password not provided']);
        exit;
    }

    $stmt = $conn->prepare("SELECT password FROM user_account WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    $passwordMatch = false;
    if ($user) {
        // First, try to verify as a hashed password
        if (password_verify($enteredPassword, $user['password'])) {
            $passwordMatch = true;
        } 
        // If that fails, check if it's a plain-text password (for legacy users)
        else if ($enteredPassword === $user['password']) {
            $passwordMatch = true;
            
            // As the password is correct, let's update it to a hashed one
            $newHashedPassword = password_hash($enteredPassword, PASSWORD_DEFAULT);
            $updateStmt = $conn->prepare("UPDATE user_account SET password = :password WHERE user_id = :user_id");
            $updateStmt->bindParam(':password', $newHashedPassword, PDO::PARAM_STR);
            $updateStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $updateStmt->execute();
        }
    }

    if ($passwordMatch) {
        echo json_encode(['success' => true, 'password' => $enteredPassword]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Incorrect password']);
    }
} else {
    // Handle GET request to fetch account details
    $stmt = $conn->prepare("SELECT username, email FROM user_account WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        echo json_encode([
            'success' => true,
            'username' => $user['username'],
            'email' => $user['email']
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'User not found']);
    }
}
?>
