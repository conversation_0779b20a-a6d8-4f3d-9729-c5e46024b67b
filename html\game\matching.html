<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matching Game - FunConnect</title>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../css/style_matching.css">
    <!-- Authentication Guard - Must be loaded before page content -->
    <script src="../../js/auth-guard.js"></script>
</head>
<body>
    <div class="game-container">
        <div class="game-layout">
            <!-- Game Area -->
            <div class="game-area">
                <!-- Options Area (Draggable) -->
                <div class="options-area" id="optionsArea">
                    <div id="draggableOptions" class="draggable-options">
                        <!-- Options will be dynamically added here -->
                    </div>
                </div>

                <!-- Question Area -->
                <div class="question-area">
                    <h3 id="questionText"></h3>
                </div>

                <!-- Drop Area -->
                <div class="drop-area" id="dropArea">
                    <div id="dropZone" class="drop-zone">Drop your answer here</div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="side-panel">
                <div class="lives-section">
                    <h4>Lives</h4>
                    <div id="livesDisplay" class="lives-display">
                        <!-- Lives will be added dynamically -->
                    </div>
                </div>
                <div class="timer-section">
                    <h4>Time</h4>
                    <p id="timer">00:00</p>
                </div>
                <div class="powerups-section">
                    <h4>Power-ups</h4>
                    <button id="addTimeBtn" class="btn powerup" title="Add 10 seconds">
                        <span class="powerup-icon">⏰</span> Add Time
                    </button>
                    <button id="showAnswerBtn" class="btn powerup" title="Show answer briefly">
                        <span class="powerup-icon">💡</span> Show Answer
                    </button>
                </div>
                <div class="controls-section">
                    <button id="exitBtn" class="btn danger">Exit Game</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Instruction Modal -->
    <div class="modal instructions-modal" id="instruction-modal" role="dialog" aria-labelledby="instruction-title">
        <div class="modal-content">
            <h2 id="instruction-title">Matching Challenge</h2>
            <div class="instructions">
                <p>Instructions</p>
                <ul>
                    <li>Drag the correct answer to the drop zone to match the question.</li>
                    <li>You have limited time for each question.</li>
                    <li>Wrong answers will cost you a life.</li>
                    <li>Use power-ups wisely to help you succeed!</li>
                </ul>
            </div>
            <button class="start-btn" id="start-btn" aria-label="Start Matching Challenge">START!</button>
        </div>
    </div>

    <!-- Results Modal -->
    <div class="modal results-modal" id="results-modal" role="dialog" aria-labelledby="result-title">
        <div class="modal-content">
            <h2 id="result-title" class="result-title">Level Complete!</h2>
            <div id="result-stars" class="stars" aria-label="Star rating"></div>
            <p id="result-message" class="result-message">You've completed the matching challenge!</p>
            <div class="exp-container">
                <span class="exp-icon">✨</span>
                <span class="exp-text">+0 EXP</span>
            </div>
            <div class="modal-buttons">
                <button class="replay-btn" id="restart-btn">PLAY AGAIN</button>
                <button class="main-menu-btn" id="main-menu-btn">MAIN MENU</button>
            </div>
        </div>
    </div>

    <!-- Sound Effects -->
    <audio id="correctSound" src="../../sounds/correct.mp3"></audio>
    <audio id="wrongSound" src="../../sounds/wrong.mp3"></audio>

    <!-- Scripts -->
    <script src="../../js/matching.js"></script>
</body>
</html> 