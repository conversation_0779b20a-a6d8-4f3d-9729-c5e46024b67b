async function fetchUserProgress() {
    try {
        const response = await fetch('../php/user_progress.php', {
            credentials: 'include',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch user progress');
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message || 'Failed to load progress data');
        }

        return data.data;
    } catch (error) {
        console.error('Error fetching progress:', error);
        showErrorToUser(error.message);
        return null;
    }
}

function updateProgressBars(progressData) {
    // Update level progress
    const levelProgress = document.getElementById('level-progress');
    const levelText = document.getElementById('level-text');
    if (levelProgress && levelText) {
        levelProgress.style.width = `${progressData.levels.progress}%`;
        levelText.textContent = `${progressData.levels.completed}/${progressData.levels.total} Levels`;
    }

    // Update tier progress
    const tierProgress = document.getElementById('tier-progress');
    const tierText = document.getElementById('tier-text');
    if (tierProgress && tierText) {
        tierProgress.style.width = `${progressData.tier.progress}%`;
        tierText.textContent = `${progressData.tier.current} (${progressData.tier.exp} EXP)`;
    }

    // Update achievements count
    const achievementsCount = document.getElementById('achievements-count');
    if (achievementsCount) {
        achievementsCount.textContent = `${progressData.achievements.total} Achievements Unlocked`;
    }
}

function displayUnlockedAchievements(achievements) {
    const container = document.getElementById('achievements-container');
    if (!container) return;

    container.innerHTML = '';

    achievements.forEach(achievement => {
        const card = document.createElement('div');
        card.className = 'achievement-card';

        const img = document.createElement('img');
        img.src = achievement.badge_image;
        img.alt = achievement.name;

        const title = document.createElement('h3');
        title.textContent = achievement.name;

        const description = document.createElement('p');
        description.textContent = achievement.description;

        const unlockedAt = document.createElement('span');
        unlockedAt.className = 'unlocked-date';
        const date = new Date(achievement.unlocked_at);
        unlockedAt.textContent = `Unlocked: ${date.toLocaleDateString()}`;

        card.appendChild(img);
        card.appendChild(title);
        card.appendChild(description);
        card.appendChild(unlockedAt);
        container.appendChild(card);
    });
}

async function displayProgress() {
    const progressData = await fetchUserProgress();
    if (!progressData) return;

    updateProgressBars(progressData);
    displayUnlockedAchievements(progressData.achievements.unlocked);
}

// Helper function to show errors to user
function showErrorToUser(message) {
    const errorContainer = document.getElementById('error-container') || createErrorContainer();
    errorContainer.textContent = message;
    errorContainer.style.display = 'block';
    
    setTimeout(() => {
        errorContainer.style.display = 'none';
    }, 5000);
}

function createErrorContainer() {
    const container = document.createElement('div');
    container.id = 'error-container';
    container.style = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px;
        background: #ffebee;
        color: #c62828;
        border: 1px solid #ef9a9a;
        border-radius: 4px;
        display: none;
        z-index: 1000;
    `;
    document.body.appendChild(container);
    return container;
}

// Call the function when the page loads
document.addEventListener('DOMContentLoaded', displayProgress);
