body {
    background-color: #0b0b23;
    color: white;
    min-height: 100vh;
    padding: 20px;
    margin: 0;
    font-family: Arial, sans-serif;
}

.game-container {
    background-color: #1a1a3a;
    border-radius: 15px;
    padding: 20px;
    min-height: 90vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    margin: 0 auto;
    max-width: 1400px;
    position: relative;
    z-index: 1;
    transition: opacity 0.5s ease-in-out; /* Default smooth transition */
}

/* Transition effect for game restart */
.game-container.fade-transition {
    opacity: 0;
}

.game-container.fade-in {
    opacity: 1;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.game-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    height: 100%;
}

/* Game Area Styles */
.game-area {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    min-height: 80vh;
}

/* Options Area */
.options-area {
    background-color: rgba(79, 195, 247, 0.1);
    border-radius: 10px;
    padding: 20px;
    min-height: 120px;
    margin-bottom: 20px;
}

.draggable-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    justify-content: center;
    align-items: start;
    padding: 10px;
}

.option-item {
    background-color: #4fc3f7;
    color: #0b0b23;
    padding: 15px;
    border-radius: 8px;
    cursor: move;
    user-select: none;
    font-weight: bold;
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    word-break: break-word;
}

.option-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    background-color: #5fd4ff;
}

.option-item.dragging {
    opacity: 0.5;
    background-color: #3d9ac7;
}

/* Question Area */
.question-area {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin: 20px 0;
}

.question-area h3 {
    font-size: 1.5em;
    margin: 0;
    line-height: 1.4;
}

/* Drop Area */
.drop-area {
    background-color: rgba(79, 195, 247, 0.1);
    border-radius: 10px;
    padding: 20px;
    min-height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.drop-zone {
    border: 2px dashed #4fc3f7;
    border-radius: 8px;
    padding: 20px;
    width: 100%;
    text-align: center;
    color: #4fc3f7;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Add a subtle pulsing animation to the drop zone */
.drop-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(79, 195, 247, 0.2) 0%, rgba(79, 195, 247, 0) 70%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.5s ease;
}

.drop-zone:hover::before {
    opacity: 1;
    animation: pulse-gentle 2s infinite;
}

.drop-zone.active {
    background-color: rgba(79, 195, 247, 0.2);
    border-style: solid;
    box-shadow: 0 0 15px rgba(79, 195, 247, 0.5);
}

/* Remove these classes as we're now handling this with inline styles for better animations */
.drop-zone.correct {
    border-color: #4caf50;
    background-color: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.drop-zone.wrong {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

/* Add a new keyframe animation for the gentle pulsing effect */
@keyframes pulse-gentle {
    0% {
        opacity: 0.4;
        transform: scale(0.95);
    }
    50% {
        opacity: 0.6;
        transform: scale(1);
    }
    100% {
        opacity: 0.4;
        transform: scale(0.95);
    }
}

/* Side Panel Styles */
.side-panel {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    height: fit-content;
}

.lives-section,
.timer-section,
.powerups-section {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: rgba(79, 195, 247, 0.1);
    border-radius: 8px;
}

.lives-section h4,
.timer-section h4,
.powerups-section h4 {
    color: #4fc3f7;
    margin-bottom: 10px;
}

.lives-display {
    display: flex;
    justify-content: center;
    gap: 5px;
    flex-wrap: wrap;
    margin: 10px 0;
}

.life-icon {
    color: #ff4444;
    font-size: 1.5em;
    animation: pulse 1s infinite;
}

.life-icon.lost {
    color: #666;
    animation: none;
    opacity: 0.5;
}

#timer {
    font-size: 2em;
    font-weight: bold;
    margin: 10px 0;
    color: white;
}

/* Power-up Styles */
.powerups-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn.powerup {
    background-color: #673ab7;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    font-size: 1em;
}

.btn.powerup:hover {
    background-color: #7e57c2;
}

.btn.powerup:disabled {
    background-color: #444;
    cursor: not-allowed;
    transform: none;
    opacity: 0.7;
}

.powerup-icon {
    font-size: 1.2em;
}

/* Highlight effect for show answer power-up */
.option-item.highlight {
    animation: highlight 0.3s ease-in-out;
    border: 2px solid #ffd700;
    box-shadow: 0 0 10px #ffd700;
}

@keyframes highlight {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Custom Button Styles */
.btn {
    width: 100%;
    padding: 12px;
    font-size: 1.1em;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    text-transform: uppercase;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn.primary {
    background-color: #4fc3f7;
    color: #0b0b23;
}

.btn.primary:hover {
    background-color: #5fd4ff;
}

.btn.danger {
    background-color: #f44336;
    color: white;
}

.btn.danger:hover {
    background-color: #ff5c4f;
}

/* Animation for correct/wrong answers */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 0.5s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-layout {
        grid-template-columns: 1fr;
    }

    .draggable-options {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .option-item {
        font-size: 0.9em;
        padding: 10px;
    }

    .question-area h3 {
        font-size: 1.2em;
    }

    #currentScore,
    #timer {
        font-size: 1.5em;
    }

    .btn {
        padding: 10px;
        font-size: 1em;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.modal.show {
    display: flex;
    opacity: 1;
    pointer-events: auto;
}

/* Hide game content when modal is shown */
.modal.show ~ .game-container {
    filter: blur(5px);
    pointer-events: none;
}

.modal-content {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.3);
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: modalAppear 0.5s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

#instruction-title, #result-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 25px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
    font-weight: normal;
}

.instructions {
    background: rgba(79, 195, 247, 0.1);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    margin-bottom: 30px;
    text-align: left;
}

.instructions p {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 20px;
    line-height: 1.6;
    text-align: center;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    line-height: 1.5;
}

.instructions li::before {
    content: '⚡';
    position: absolute;
    left: 0;
    color: #ffeb3b;
    font-size: 1.2rem;
}

.stars {
    font-size: 2.5rem;
    margin: 20px 0;
    letter-spacing: 10px;
}

#result-message {
    color: #fff;
    font-size: 1.2rem;
    margin: 20px 0;
    line-height: 1.5;
}

.exp-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: expPulse 2s infinite;
}

@keyframes expPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(79, 195, 247, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 25px rgba(79, 195, 247, 0.4);
    }
}

.exp-icon {
    font-size: 1.8rem;
    animation: sparkle 1.5s infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(15deg);
        opacity: 0.8;
    }
}

.exp-text {
    color: #ffeb3b;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
}

.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

#start-btn, .replay-btn, .main-menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#start-btn, .replay-btn {
    background: linear-gradient(145deg, #2196F3, #1e88e5);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.main-menu-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

#start-btn:hover, .replay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.main-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .modal-content {
        padding: 30px;
        width: 95%;
    }

    #instruction-title, #result-title {
        font-size: 2rem;
    }

    .stars {
        font-size: 2rem;
    }

    .exp-container {
        padding: 12px;
    }

    .exp-icon {
        font-size: 1.5rem;
    }

    .exp-text {
        font-size: 1.3rem;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 15px;
    }

    #start-btn, .replay-btn, .main-menu-btn {
        width: 100%;
        min-width: unset;
        padding: 12px 20px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .modal-content {
        padding: 20px;
    }

    #instruction-title, #result-title {
        font-size: 1.8rem;
        margin-bottom: 20px;
    }

    .instructions {
        padding: 15px;
    }

    .instructions p {
        font-size: 1.1rem;
    }

    .instructions li {
        font-size: 1rem;
        margin-bottom: 12px;
    }

    #result-message {
        font-size: 1.1rem;
    }

    .stars {
        font-size: 1.8rem;
        letter-spacing: 8px;
    }
}

/* Star and EXP animations */
.stars.animate-in {
    animation: starAppear 0.5s ease-out forwards;
}

.stars.perfect-score {
    animation: perfectScore 1s ease-in-out infinite;
}

@keyframes starAppear {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes perfectScore {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: scale(1);
    }
}

.exp-container.animate-in {
    animation: expAppear 0.8s ease-out forwards;
}

.exp-container.perfect-score {
    animation: expPerfect 1.2s ease-in-out infinite;
}

@keyframes expAppear {
    0% {
        transform: translateY(20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes expPerfect {
    0% {
        transform: scale(1);
        background: rgba(255, 235, 59, 0.1);
    }
    50% {
        transform: scale(1.05);
        background: rgba(255, 235, 59, 0.2);
        box-shadow: 0 0 15px rgba(255, 235, 59, 0.3);
    }
    100% {
        transform: scale(1);
        background: rgba(255, 235, 59, 0.1);
    }
}

/* EXP indicator animation */
.exp-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #4caf50;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
    animation: float-up 1.5s ease-out forwards;
    z-index: 10;
}

@keyframes float-up {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -100%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -150%) scale(0.8);
    }
}

/* Star animation for results */
.animate-in {
    animation: pop-in 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.perfect-score {
    animation: perfect-score 1s ease-in-out infinite alternate;
}

@keyframes pop-in {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    70% {
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes perfect-score {
    0% {
        transform: scale(1);
        text-shadow: 0 0 5px gold;
    }
    100% {
        transform: scale(1.1);
        text-shadow: 0 0 15px gold, 0 0 30px yellow;
    }
}

/* Loading spinner for transitions */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 5px solid rgba(79, 195, 247, 0.2);
    border-top: 5px solid #4fc3f7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.loading-spinner.show {
    opacity: 1;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
} 