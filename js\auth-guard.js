/**
 * Authentication Guard Utility
 * Provides centralized authentication checking and redirect functionality
 * Integrates with existing PHP session system via get_user_session.php
 */

class AuthGuard {
    constructor() {
        this.isCheckingAuth = false;
        this.authCheckPromise = null;
        this.excludedPages = [
            'homepage.html',
            'login.html', 
            'signup.html'
        ];
    }

    /**
     * Check if current page should be excluded from authentication
     * @returns {boolean} True if page should be excluded
     */
    isExcludedPage() {
        const currentPage = window.location.pathname.split('/').pop();
        return this.excludedPages.includes(currentPage);
    }

    /**
     * Get the appropriate redirect path based on current location
     * @returns {string} Relative path to login.html
     */
    getLoginRedirectPath() {
        const currentPath = window.location.pathname;

        // If we're in a game subdirectory
        if (currentPath.includes('/game/')) {
            return '../../html/login.html';
        }
        // If we're in the html directory
        else if (currentPath.includes('/html/')) {
            return 'login.html';
        }
        // Handle direct access or other cases
        else {
            return 'html/login.html';
        }
    }

    /**
     * Check authentication status with the server
     * @returns {Promise<Object>} Authentication result
     */
    async checkAuthenticationStatus() {
        // Prevent multiple simultaneous auth checks
        if (this.isCheckingAuth && this.authCheckPromise) {
            return this.authCheckPromise;
        }

        this.isCheckingAuth = true;
        
        this.authCheckPromise = new Promise(async (resolve, reject) => {
            try {
                // Determine the correct path to get_user_session.php
                let sessionCheckPath = '../php/get_user_session.php';
                const currentPath = window.location.pathname;

                if (currentPath.includes('/game/')) {
                    sessionCheckPath = '../../php/get_user_session.php';
                } else if (currentPath.includes('/html/')) {
                    sessionCheckPath = '../php/get_user_session.php';
                } else {
                    // Handle direct access or other cases
                    sessionCheckPath = 'php/get_user_session.php';
                }

                const response = await fetch(sessionCheckPath, {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                resolve(data);
            } catch (error) {
                console.error('Authentication check failed:', error);
                // On network error, assume not authenticated for security
                resolve({ success: false, message: 'Authentication check failed' });
            } finally {
                this.isCheckingAuth = false;
                this.authCheckPromise = null;
            }
        });

        return this.authCheckPromise;
    }

    /**
     * Perform authentication check and redirect if necessary
     * This is the main method to be called on page load
     * @param {Object} options Configuration options
     * @param {boolean} options.showLoader Show loading indicator during check
     * @param {Function} options.onAuthenticated Callback when user is authenticated
     * @param {Function} options.onUnauthenticated Callback when user is not authenticated
     */
    async guardPage(options = {}) {
        // Skip authentication for excluded pages
        if (this.isExcludedPage()) {
            console.log('AuthGuard: Skipping authentication check for excluded page');
            if (options.onAuthenticated) {
                options.onAuthenticated({ skipped: true });
            }
            return;
        }

        // Show loader if requested
        if (options.showLoader) {
            this.showAuthLoader();
        }

        try {
            console.log('AuthGuard: Checking authentication status...');
            const authResult = await this.checkAuthenticationStatus();

            if (authResult.success && authResult.userId) {
                console.log('AuthGuard: User is authenticated, userId:', authResult.userId);
                
                // Hide loader
                if (options.showLoader) {
                    this.hideAuthLoader();
                }

                // Call authenticated callback
                if (options.onAuthenticated) {
                    options.onAuthenticated(authResult);
                }
            } else {
                console.log('AuthGuard: User is not authenticated, redirecting to login...');
                
                // Call unauthenticated callback before redirect
                if (options.onUnauthenticated) {
                    options.onUnauthenticated(authResult);
                }

                // Redirect to login page
                this.redirectToLogin();
            }
        } catch (error) {
            console.error('AuthGuard: Error during authentication check:', error);
            
            // Hide loader on error
            if (options.showLoader) {
                this.hideAuthLoader();
            }

            // On error, redirect to login for security
            this.redirectToLogin();
        }
    }

    /**
     * Redirect to login page
     */
    redirectToLogin() {
        const loginPath = this.getLoginRedirectPath();
        console.log('AuthGuard: Redirecting to:', loginPath);
        
        // Use replace to prevent back button issues
        window.location.replace(loginPath);
    }

    /**
     * Show authentication loading indicator
     */
    showAuthLoader() {
        // Create loader if it doesn't exist
        if (!document.getElementById('auth-loader')) {
            const loader = document.createElement('div');
            loader.id = 'auth-loader';
            loader.innerHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    font-family: Arial, sans-serif;
                ">
                    <div style="
                        background: white;
                        padding: 20px;
                        border-radius: 8px;
                        text-align: center;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    ">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #3498db;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 10px;
                        "></div>
                        <p style="margin: 0; color: #333;">Verifying authentication...</p>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
            document.body.appendChild(loader);
        }
    }

    /**
     * Hide authentication loading indicator
     */
    hideAuthLoader() {
        const loader = document.getElementById('auth-loader');
        if (loader) {
            loader.remove();
        }
    }

    /**
     * Quick authentication check without redirect
     * Useful for conditional UI elements
     * @returns {Promise<boolean>} True if authenticated
     */
    async isAuthenticated() {
        try {
            const result = await this.checkAuthenticationStatus();
            return result.success && result.userId;
        } catch (error) {
            console.error('AuthGuard: Quick auth check failed:', error);
            return false;
        }
    }

    /**
     * Get current user ID if authenticated
     * @returns {Promise<number|null>} User ID or null
     */
    async getCurrentUserId() {
        try {
            const result = await this.checkAuthenticationStatus();
            return result.success ? result.userId : null;
        } catch (error) {
            console.error('AuthGuard: Failed to get user ID:', error);
            return null;
        }
    }
}

// Create global instance
window.AuthGuard = new AuthGuard();

// Auto-initialize on DOM content loaded if not excluded page
document.addEventListener('DOMContentLoaded', function() {
    // Only auto-guard if the page hasn't explicitly disabled it
    if (!window.AUTH_GUARD_DISABLED && !window.AuthGuard.isExcludedPage()) {
        window.AuthGuard.guardPage({
            showLoader: true,
            onAuthenticated: function(result) {
                console.log('AuthGuard: Page access granted for user:', result.userId);
            },
            onUnauthenticated: function(result) {
                console.log('AuthGuard: Access denied, redirecting to login');
            }
        });
    }
});

// Export for module systems if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthGuard;
}
