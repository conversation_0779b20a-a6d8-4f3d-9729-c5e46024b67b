@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500&display=swap');

body{   
    background-color: rgb(11, 8, 16);
    font-family: "Cinzel", serif;
}   

p{
    margin: 0px;
}

/* Text transformation to ensure proper capitalization */
.title, .login-title, .signup-title, button, label p {
    text-transform: capitalize;
}

input {
    text-transform: none;
}

/* Input specific styles for better readability */
input, input::placeholder {
    font-family: 'Open Sans', Arial, sans-serif !important;
    letter-spacing: 0.5px;
}

/* HEADER AND TITLE DESIGN */
.header{
    background-color: transparent;
    border-radius: 10px;
    display: flex;
    min-width: 100%;
    min-height: 20vh;
    margin-top: 2%;
}   

.header .title{
    padding-top: 10px;
    padding-left: 8%;
}

.body{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 50vh;
}

.title{
    font-size: 4em;
    font-family: "Cinzel", serif;
    color: white;
    margin-bottom: 40px;
    text-shadow: 0px 0px 5px rgb(255, 255, 255), 0px 0px 15px rgb(255, 255, 255);
}

.title a{
    text-decoration: none;
    color: white;
}

/* LOGIN AND SIGNUP DESIGN */
.login-cont, .signup-cont{
    border: 1px solid white;
    border-radius: 24px;
    padding: 10px;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bottomToTop .8s 1 ease-in-out;
}

@keyframes bottomToTop {
    from {
        opacity: 0;
        transform: translateY(10%);
    }
    to {
        opacity: 1;
        transform: translateY(0%);
    }
}

.login-cont .login-title, .signup-cont .signup-title{
    font-size: 2em;
    font-family: "Cinzel", serif;
    margin: 10px;
    padding-bottom: 20px;
}

.login-cont form, .signup-cont form{
    display: flex;
    flex-direction: column;
}

.login-cont form label, .signup-cont form label{
    font-size: 1em;
    font-family: "Cinzel", serif;
    margin-left: 10px;
    color: white;   
}

.login-cont form input, .signup-cont form input{
    margin: 10px;
    padding: 10px 10px;
    font-size: 1.2em;
    font-family: 'Open Sans', Arial, sans-serif;
    border-radius: 5px;
    border: 1px solid white;
    background-color: transparent;
    width: 400px;
    color: white;
}

.btn-cont{
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    margin-bottom: 20px;
}

.btn-cont button{
    padding: 10px;
    font-size: .9em;
    font-family: "Cinzel", serif;
    border-radius: 5px;
    padding: 5px 15px;
    border: 0px;
    background-color: rgb(0, 7, 138);
    color: white;
    cursor: pointer;
}

.btn-cont a{
    padding: 10px;
    font-size: .9em;
    font-family: "Cinzel", serif;
    padding: 5px 15px;
    color: white;
    text-decoration: underline;
}

/* Error message styling */
.error-message {
    color: #ff3333;
    font-size: 0.9em;
    margin: 5px 10px;
    display: none;
    text-align: center;
    font-family: 'Open Sans', Arial, sans-serif;
}

/* Custom Modal Styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
    text-align: center;
}

.modal-content {
    position: relative;
    background-color: rgb(20, 20, 30);
    margin: 0;
    padding: 0;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    width: 40%;
    max-width: 450px;
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
    animation: modalFadeIn 0.2s ease-in-out;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -55%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.modal-header {
    padding: 12px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.5);
}

.modal-title {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-family: "Cinzel", serif;
    font-size: 1.3em;
}

.modal-body {
    padding: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Open Sans', Arial, sans-serif;
    font-size: 0.95em;
}

.modal-footer {
    padding: 12px;
    border-top: 1px solid rgba(68, 68, 68, 0.5);
    display: flex;
    justify-content: flex-end;
}

.modal-button {
    padding: 6px 14px;
    margin-left: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: "Cinzel", serif;
    font-size: 0.85em;
    background-color: rgba(68, 68, 68, 0.8);
    color: rgba(255, 255, 255, 0.9);
    transition: background-color 0.2s;
}

.modal-button:hover {
    background-color: rgba(68, 68, 68, 1);
}

.modal-button.primary {
    background-color: rgba(0, 7, 138, 0.8);
}

.modal-button.primary:hover {
    background-color: rgba(0, 7, 138, 1);
}