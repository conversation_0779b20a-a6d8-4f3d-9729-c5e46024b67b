// Signup form validation and modal handling
document.addEventListener('DOMContentLoaded', function() {
    const signupForm = document.getElementById('signup_form');
    const signupButton = document.getElementById('signup');
    const modal = document.getElementById('custom-modal');
    const modalYesButton = document.getElementById('modal-yes-button');
    const modalNoButton = document.getElementById('modal-no-button');
    const signupError = document.getElementById('signup-error');
    const passwordLabel = document.getElementById('password_lbl');
    const confirmPassLabel = document.getElementById('confirmpass_lbl');

    // Check URL parameters for errors
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('error')) {
        const errorType = urlParams.get('error');
        let errorMessage = 'An error occurred. Please try again.';
        
        switch(errorType) {
            case 'username_exists':
                errorMessage = 'Username already exists. Please choose a different username.';
                break;
            case 'db_error':
                errorMessage = 'Database error. Please try again later.';
                break;
        }
        
        if (signupError) {
            signupError.textContent = errorMessage;
            signupError.style.display = 'block';
        }
    }

    // Handle form submission
    if (signupButton) {
        signupButton.addEventListener('click', function(event) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const confirmPassword = document.getElementById('confirmpass').value.trim();
            
            // Basic validation
            if (username !== "" && password !== "" && confirmPassword !== "") {
                event.preventDefault();
                confirmPass();
            }
        });
    }

    // Password confirmation function
    function confirmPass() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmpass').value;

        if (password !== confirmPassword) {
            // Reset labels to default state
            resetLabels();
            
            // Show error message
            confirmPassLabel.querySelector('p').textContent = "Passwords do not match!";
            confirmPassLabel.style.color = "red";
            passwordLabel.querySelector('p').textContent = "Passwords do not match!";
            passwordLabel.style.color = "red";
            
            if (signupError) {
                signupError.textContent = "Passwords do not match!";
                signupError.style.display = "block";
            }
        } else {
            // Reset labels and show confirmation modal
            resetLabels();
            showModal();
        }
    }

    // Reset labels to default state
    function resetLabels() {
        if (passwordLabel) {
            passwordLabel.querySelector('p').textContent = "Password";
            passwordLabel.style.color = "";
        }
        
        if (confirmPassLabel) {
            confirmPassLabel.querySelector('p').textContent = "Confirm Password";
            confirmPassLabel.style.color = "";
        }
        
        if (signupError) {
            signupError.textContent = "";
            signupError.style.display = "none";
        }
    }

    // Modal handling
    function showModal() {
        if (modal) modal.style.display = 'block';
    }

    function closeModal() {
        if (modal) modal.style.display = 'none';
        
        // Clean up URL parameters after closing modal
        const url = new URL(window.location.href);
        url.search = '';
        window.history.replaceState({}, document.title, url.toString());
    }

    // Modal button handlers
    if (modalYesButton) {
        modalYesButton.addEventListener('click', function() {
            createAccount();
        });
    }

    if (modalNoButton) {
        modalNoButton.addEventListener('click', function() {
            closeModal();
        });
    }

    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });

    // Close modal when pressing Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal && modal.style.display === 'block') {
            closeModal();
        }
    });

    // Create account function
    function createAccount() {
        if (signupForm) signupForm.submit();
    }
});
