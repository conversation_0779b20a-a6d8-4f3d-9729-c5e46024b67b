@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');

/* Base Styles */
:root {
    --bg-dark: rgb(11, 8, 16);
    --bg-darker: rgb(5, 3, 10);
    --bg-light: rgb(30, 25, 40);
    --primary: #8a63f2;
    --primary-dark: #6a4bc7;
    --secondary: #ff9e3f;
    --success: #6fcf97;
    --warning: #f2c94c;
    --danger: #eb5757;
    --light: rgba(255, 255, 255, 0.9);
    --dark: rgba(0, 0, 0, 0.8);
    --gray: rgba(255, 255, 255, 0.1);
    --dark-gray: rgba(255, 255, 255, 0.4);
    --text: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--bg-dark);
    color: var(--text);
    line-height: 1.6;
    min-height: 100vh;
    padding: 2rem;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-light);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray);
}

.header-con {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header p {
    font-size: 1.8rem;
    color: var(--primary);
    text-shadow: 0 0 10px rgba(138, 99, 242, 0.4);
}

.header-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s;
}

.header-close:hover {
    color: var(--text);
}

.body {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.card-container {
    perspective: 1000px;
}

.card {
    background: var(--bg-darker);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid var(--gray);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.card.locked {
    opacity: 0.7;
    background: var(--bg-darker);
}

.card.locked:hover {
    transform: none;
    box-shadow: none;
}

.card img {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin-bottom: 1rem;
    transition: filter 0.3s;
}

.card.locked img.grayscale {
    filter: grayscale(100%);
}

.achievement-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.achievement-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.progress-container {
    margin-top: 1rem;
}

.progress-bar {
    height: 8px;
    background: var(--gray);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s;
}

.card.locked:hover .lock-overlay {
    opacity: 1;
}

/* X Button Styles */
.x-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--gray);
}

.x-button:hover {
    background-color: var(--danger);
    transform: scale(1.1);
}

.x-button img {
    width: 20px;
    height: 20px;
    filter: invert(1);
}