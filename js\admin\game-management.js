// Global variables
let gameLevels = [];
let filteredGameLevels = [];
let gameContent = [];

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Pending changes system
let pendingChanges = new Map(); // Map<levelID, Map<questionID, questionData>>
let originalQuestions = new Map(); // Map<questionID, originalQuestionData>

// Track expanded levels to preserve state during refresh
let expandedLevels = new Set(); // Set<levelID>

// Initialize game content data
async function initializeGameData() {
    try {
        await loadGameContent();
        displayGameLevels();
    } catch (error) {
        console.error('Error initializing game data:', error);
        showNotification('Error loading game content from database', 'error');
    }
}

// Load game content from API
async function loadGameContent() {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
        const result = await response.json();

        if (result.success) {
            gameContent = result.data;
            processGameLevels();
        } else {
            throw new Error(result.error || 'Failed to load game content');
        }
    } catch (error) {
        console.error('Error loading game content:', error);
        showNotification(error.message || 'Error loading game content', 'error');
        gameContent = [];
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Process game content into levels
function processGameLevels() {
    const levelGroups = {};

    gameContent.forEach(content => {
        const levelNum = content.level_number;
        if (!levelGroups[levelNum]) {
            levelGroups[levelNum] = {
                levelID: levelNum,
                levelName: `Level ${levelNum}`,
                canEdit: content.can_edit == 1, // Convert to boolean
                questions: []
            };
        }

        // Update canEdit to false if any question in the level has can_edit = 0
        if (content.can_edit == 0) {
            levelGroups[levelNum].canEdit = false;
        }

        levelGroups[levelNum].questions.push({
            id: content.content_id,
            question: content.question_text,
            options: [content.option1, content.option2, content.option3, content.option4],
            correctAnswer: content.correct_answer,
            canEdit: content.can_edit == 1,
            quizType: content.quiz_type || 'Multiple Choice'
        });
    });

    gameLevels = Object.values(levelGroups).sort((a, b) => a.levelID - b.levelID);
    filteredGameLevels = [...gameLevels];

    if (gameLevels.length === 0) {
        gameLevels = [];
        filteredGameLevels = [];
    }

    // Store original questions for comparison
    storeOriginalQuestions();
}

// Store original questions for pending changes comparison
function storeOriginalQuestions() {
    originalQuestions.clear();
    gameLevels.forEach(level => {
        level.questions.forEach(question => {
            originalQuestions.set(question.id, {
                id: question.id,
                question: question.question,
                options: [...question.options],
                correctAnswer: question.correctAnswer,
                levelID: level.levelID
            });
        });
    });
}

// Check if a question has pending changes
function hasQuestionPendingChanges(questionId, levelID) {
    const levelChanges = pendingChanges.get(levelID);
    return levelChanges && levelChanges.has(questionId);
}

// Get pending changes for a question
function getPendingQuestion(questionId, levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (levelChanges && levelChanges.has(questionId)) {
        return levelChanges.get(questionId);
    }
    return null;
}

// Add or update pending changes for a question
function setPendingQuestion(questionId, levelID, questionData) {
    if (!pendingChanges.has(levelID)) {
        pendingChanges.set(levelID, new Map());
    }
    const levelChanges = pendingChanges.get(levelID);
    levelChanges.set(questionId, questionData);
}

// Remove pending changes for a question
function removePendingQuestion(questionId, levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (levelChanges) {
        levelChanges.delete(questionId);
        if (levelChanges.size === 0) {
            pendingChanges.delete(levelID);
        }
    }
}

// Check if a level has any pending changes
function hasLevelPendingChanges(levelID) {
    const levelChanges = pendingChanges.get(levelID);
    return levelChanges && levelChanges.size > 0;
}

// Get the current question data (pending or original)
function getCurrentQuestionData(questionId, levelID) {
    const pendingQuestion = getPendingQuestion(questionId, levelID);
    if (pendingQuestion) {
        return pendingQuestion;
    }

    const level = gameLevels.find(l => l.levelID === levelID);
    if (level) {
        const question = level.questions.find(q => q.id === questionId);
        if (question) {
            return question;
        }
    }

    return null;
}

// Display game levels
function displayGameLevels() {
    // Store current expanded state before refreshing
    storeExpandedState();

    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    if (filteredGameLevels.length === 0) {
        const isSearching = document.getElementById('levelSearch').value.trim() !== '';
        levelsContainer.innerHTML = `
            <div class="empty-levels-state">
                <div class="empty-icon">
                    <i class="fas fa-${isSearching ? 'search' : 'gamepad'}"></i>
                </div>
                <h3>${isSearching ? 'No Levels Found' : 'No Game Levels Found'}</h3>
                <p>${isSearching ? 'No levels match your search criteria. Try different keywords.' : 'No levels with questions have been created yet. Add some questions to create levels.'}</p>
                ${!isSearching ? '<button class="btn-primary" onclick="showAddQuestionModal(1)"><i class="fas fa-plus"></i> Add First Question</button>' : ''}
            </div>
        `;
        return;
    }

    filteredGameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });

    // Restore expanded state after creating cards
    restoreExpandedState();
}

// Store the current expanded state of levels
function storeExpandedState() {
    expandedLevels.clear();
    filteredGameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        if (questionsContainer && questionsContainer.classList.contains('expanded')) {
            expandedLevels.add(level.levelID);
        }
    });
}

// Restore the expanded state of levels
function restoreExpandedState() {
    expandedLevels.forEach(levelID => {
        const questionsContainer = document.getElementById(`level-questions-${levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

        if (questionsContainer && dropdownToggle) {
            questionsContainer.classList.remove('collapsed');
            questionsContainer.classList.add('expanded');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        }
    });
}

// Create level card HTML
function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        // Check if this question has pending changes
        const hasPendingChanges = hasQuestionPendingChanges(question.id, level.levelID);
        const currentQuestion = hasPendingChanges ? getPendingQuestion(question.id, level.levelID) : question;

        let optionsHtml = '';
        if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
            const optionLabels = ['A', 'B', 'C', 'D'];
            optionsHtml = currentQuestion.options.map((option, index) => {
                const correctIndex = typeof currentQuestion.correctAnswer === 'string'
                    ? optionLabels.indexOf(currentQuestion.correctAnswer.toUpperCase())
                    : currentQuestion.correctAnswer - 1;
                const isCorrect = index === correctIndex;
                return `<li>
                    <span class="option-label">${optionLabels[index]}:</span>
                    <span class="option-text">${option}</span>
                    ${isCorrect ? '<span class="correct-answer">Correct</span>' : ''}
                </li>`;
            }).join('');
        }

        return `<div class="question-item ${hasPendingChanges ? 'has-pending-changes' : ''}">
            <div class="question-header">
                <div class="question-text">
                    ${currentQuestion.question}
                    ${hasPendingChanges ? '<span class="pending-indicator" title="Has unsaved changes"><i class="fas fa-circle"></i></span>' : ''}
                </div>
                <div class="question-actions">
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})" ${!question.canEdit ? 'disabled title="Editing disabled for this question"' : ''}>
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})" ${!question.canEdit ? 'disabled title="Deletion disabled for this question"' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="question-meta">
                <span>Level ${level.levelID}</span>
                ${hasPendingChanges ? '<span class="pending-changes-text">Unsaved changes</span>' : ''}
                <span class="quiz-type-label">${currentQuestion.quizType || 'Multiple Choice'}</span>
            </div>
            ${(currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice' ? `<ul class="options-list">${optionsHtml}</ul>` : ''}
            ${(currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? `<div class="matching-answer"><b>Correct Answer:</b> ${currentQuestion.correctAnswer}</div>` : ''}
        </div>`;
    }).join('');

    // Check if this level has pending changes
    const levelHasPendingChanges = hasLevelPendingChanges(level.levelID);

    card.innerHTML = `
        <div class="level-header" onclick="${level.canEdit ? `toggleLevelDropdown(${level.levelID})` : ''}" ${!level.canEdit ? 'style="cursor: default;" title="Expansion disabled - editing not allowed for this level"' : ''}>
            <div class="level-info">
                <div class="level-title-row">
                    <h3>${level.levelName} ${levelHasPendingChanges ? '<span class="level-pending-indicator" title="Has unsaved changes"><i class="fas fa-exclamation-circle"></i></span>' : ''} ${!level.canEdit ? '<span class="edit-disabled-indicator" title="Editing disabled"><i class="fas fa-lock"></i></span>' : ''}</h3>
                    <div class="level-meta">
                        <span class="questions-count">${level.questions.length} questions</span>
                        ${levelHasPendingChanges ? '<span class="pending-changes-count">Unsaved changes</span>' : ''}
                        ${!level.canEdit ? '<span class="edit-disabled-text">Read-only</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="level-header-actions" onclick="event.stopPropagation()">
                ${levelHasPendingChanges && level.canEdit ? `
                    <button class="btn-small btn-success" onclick="saveLevelChanges(${level.levelID})" title="Save all changes for this level">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                    <button class="btn-small btn-secondary" onclick="discardLevelChanges(${level.levelID})" title="Discard all changes for this level">
                        <i class="fas fa-undo"></i> Discard
                    </button>
                ` : ''}
                ${level.canEdit ? `
                    <button class="btn-small btn-primary" onclick="showAddQuestionModal(${level.levelID})" title="Add new question">
                        <i class="fas fa-plus"></i> Add Question
                    </button>
                ` : ''}
                ${level.canEdit ? `
                    <button class="dropdown-toggle" id="dropdown-toggle-${level.levelID}">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                ` : ''}
            </div>
        </div>
        <div class="level-questions collapsed ${level.questions.length === 0 ? 'empty' : ''}" id="level-questions-${level.levelID}">
            ${level.questions.length === 0 ?
                `<div class="empty-questions">${level.canEdit ? 'No questions added yet. Click "Add Question" to get started.' : 'No questions available in this level.'}</div>` :
                questionsHtml
            }
        </div>
    `;

    return card;
}

// Search levels
function searchLevels() {
    const searchTerm = document.getElementById('levelSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredGameLevels = [...gameLevels];
    } else {
        filteredGameLevels = gameLevels.filter(level => {
            return level.levelID.toString().includes(searchTerm);
        });
    }

    displayGameLevels();
}

// Toggle level dropdown
function toggleLevelDropdown(levelID) {
    // Check if the level can be edited
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level || !level.canEdit) {
        return; // Don't allow expansion if editing is disabled
    }

    const questionsContainer = document.getElementById(`level-questions-${levelID}`);
    const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

    if (questionsContainer && dropdownToggle) {
        if (questionsContainer.classList.contains('collapsed')) {
            questionsContainer.classList.remove('collapsed');
            questionsContainer.classList.add('expanded');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
            expandedLevels.add(levelID); // Track expanded state
        } else {
            questionsContainer.classList.remove('expanded');
            questionsContainer.classList.add('collapsed');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
            expandedLevels.delete(levelID); // Remove from expanded state
        }
    }
}

// Toggle all levels
function toggleAllLevels() {
    const toggleButton = document.getElementById('toggleAllLevels');

    if (filteredGameLevels.length === 0) {
        return;
    }

    const isExpanding = toggleButton.textContent.includes('Expand');

    filteredGameLevels.forEach(level => {
        // Only toggle levels that can be edited
        if (!level.canEdit) {
            return;
        }

        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${level.levelID}`);

        if (questionsContainer && dropdownToggle) {
            if (isExpanding) {
                questionsContainer.classList.remove('collapsed');
                questionsContainer.classList.add('expanded');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
                expandedLevels.add(level.levelID); // Track expanded state
            } else {
                questionsContainer.classList.remove('expanded');
                questionsContainer.classList.add('collapsed');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
                expandedLevels.delete(level.levelID); // Remove from expanded state
            }
        }
    });

    if (isExpanding) {
        toggleButton.innerHTML = '<i class="fas fa-compress-alt"></i> Collapse All';
    } else {
        toggleButton.innerHTML = '<i class="fas fa-expand-alt"></i> Expand All';
    }
}

// Utility: Show/hide option fields based on quiz_type
function updateModalFieldsByQuizType(quizType) {
    const optionFields = document.querySelectorAll('.option-field');
    const correctAnswerInput = document.getElementById('correctAnswer');
    const correctAnswerLabel = correctAnswerInput.previousElementSibling;
    const infoText = correctAnswerInput.nextElementSibling;
    if (quizType === 'Multiple Choice') {
        optionFields.forEach(f => f.style.display = 'block');
        correctAnswerLabel.textContent = 'Correct Answer:';
        correctAnswerInput.placeholder = 'Type the exact answer here';
        if (infoText) infoText.style.display = 'block';
    } else {
        optionFields.forEach(f => f.style.display = 'none');
        correctAnswerLabel.textContent = 'Correct Answers:';
        correctAnswerInput.placeholder = 'Type the correct answer(s) here';
        if (infoText) infoText.style.display = 'none';
    }
}

// Add event listener for quizType select
if (document.getElementById('quizType')) {
    document.getElementById('quizType').addEventListener('change', function() {
        updateModalFieldsByQuizType(this.value);
    });
}

// Show add question modal
function showAddQuestionModal(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level || !level.canEdit) {
        showNotification('Adding questions is disabled for this level', 'error');
        return;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('questionForm').reset(); // Reset first
    document.getElementById('levelNumber').value = levelID; // Then set the level number
    document.getElementById('contentId').value = '';
    document.getElementById('quizType').value = 'Multiple Choice';
    updateModalFieldsByQuizType('Multiple Choice');
    modal.classList.add('show');
}

// Edit question
function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    // Check if editing is allowed for this level
    if (!level.canEdit) {
        showNotification('Editing is disabled for this level', 'error');
        return;
    }

    // Get current question data (pending changes or original)
    const currentQuestion = getCurrentQuestionData(questionId, levelID);
    if (!currentQuestion) return;

    // Check if editing is allowed for this specific question
    const originalQuestion = level.questions.find(q => q.id === questionId);
    if (originalQuestion && !originalQuestion.canEdit) {
        showNotification('Editing is disabled for this question', 'error');
        return;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('contentId').value = questionId;
    document.getElementById('questionText').value = currentQuestion.question;
    document.getElementById('levelNumber').value = levelID;
    // Set quizType and update fields
    document.getElementById('quizType').value = currentQuestion.quizType || 'Multiple Choice';
    updateModalFieldsByQuizType(document.getElementById('quizType').value);
    if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
        document.getElementById('option1').value = currentQuestion.options[0];
        document.getElementById('option2').value = currentQuestion.options[1];
        document.getElementById('option3').value = currentQuestion.options[2];
        document.getElementById('option4').value = currentQuestion.options[3];
    } else {
        document.getElementById('option1').value = '';
        document.getElementById('option2').value = '';
        document.getElementById('option3').value = '';
        document.getElementById('option4').value = '';
    }
    document.getElementById('correctAnswer').value = currentQuestion.correctAnswer;
    modal.classList.add('show');
}

// Handle add/edit question form submission
async function handleAddQuestion(event) {
    event.preventDefault();

    const quizType = document.getElementById('quizType').value;
    const questionText = document.getElementById('questionText').value;
    const correctAnswer = document.getElementById('correctAnswer').value;
    const levelNumber = parseInt(document.getElementById('levelNumber').value);
    const contentId = document.getElementById('contentId').value;
    let option1 = '', option2 = '', option3 = '', option4 = '';
    if (quizType === 'Multiple Choice') {
        option1 = document.getElementById('option1').value;
        option2 = document.getElementById('option2').value;
        option3 = document.getElementById('option3').value;
        option4 = document.getElementById('option4').value;
        // Validation: correct answer must match one of the options
        if (![option1, option2, option3, option4].includes(correctAnswer)) {
            showNotification('The correct answer must exactly match one of the options above.', 'error');
            return;
        }
    }
    // For Matching Type, only questionText and correctAnswer are required

    const isEdit = contentId !== '';

    if (isEdit) {
        // Store pending changes for existing question
        const questionId = parseInt(contentId);
        const questionData = {
            id: questionId,
            question: questionText,
            options: quizType === 'Multiple Choice' ? [option1, option2, option3, option4] : ['', '', '', ''],
            correctAnswer: correctAnswer,
            levelID: levelNumber,
            quizType: quizType
        };
        setPendingQuestion(questionId, levelNumber, questionData);
        expandedLevels.add(levelNumber);
        showNotification('Question changes saved locally. Click "Save Changes" to apply to database.', 'info');
    } else {
        // For new questions, save immediately (existing behavior)
        const formData = new FormData();
        formData.append('quiz_type', quizType);
        formData.append('question_text', questionText);
        formData.append('option1', option1);
        formData.append('option2', option2);
        formData.append('option3', option3);
        formData.append('option4', option4);
        formData.append('correct_answer', correctAnswer);
        formData.append('level_number', levelNumber);
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
                method: 'POST',
                body: formData
            });
            const result = await response.json();
            if (result.success) {
                showNotification('Question added successfully', 'success');
                await loadGameContent(); // Refresh data
            } else {
                throw new Error(result.error || 'Failed to add question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
            return;
        }
    }
    closeModal('questionModal');
    displayGameLevels(); // Refresh display to show pending changes
}

// Save all pending changes for a specific level
async function saveLevelChanges(levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (!levelChanges || levelChanges.size === 0) {
        showNotification('No pending changes to save for this level.', 'info');
        return;
    }
    const changesArray = Array.from(levelChanges.values());
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    showNotification(`Saving ${changesArray.length} question changes...`, 'info');
    for (const questionData of changesArray) {
        try {
            const updateData = {
                content_id: questionData.id,
                question_text: questionData.question,
                option1: questionData.options[0],
                option2: questionData.options[1],
                option3: questionData.options[2],
                option4: questionData.options[3],
                correct_answer: questionData.correctAnswer,
                level_number: questionData.levelID,
                quiz_type: questionData.quizType || 'Multiple Choice'
            };
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=update_content&content_id=${questionData.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const textResponse = await response.text();
                throw new Error(`Server returned non-JSON response: ${textResponse.substring(0, 200)}...`);
            }
            const result = await response.json();
            if (result.success) {
                successCount++;
                // Remove from pending changes
                removePendingQuestion(questionData.id, levelID);
            } else {
                errorCount++;
                errors.push(`Question ${questionData.id}: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            errorCount++;
            errors.push(`Question ${questionData.id}: ${error.message}`);
            console.error(`Error updating question ${questionData.id}:`, error);
        }
    }
    // Show results
    if (successCount > 0 && errorCount === 0) {
        showNotification(`Successfully saved ${successCount} question changes!`, 'success');
        await loadGameContent(); // Refresh data from database
    } else if (successCount > 0 && errorCount > 0) {
        showNotification(`Saved ${successCount} questions, but ${errorCount} failed. Check console for details.`, 'warning');
        console.error('Save errors:', errors);
        await loadGameContent(); // Refresh data from database
    } else {
        showNotification(`Failed to save changes. ${errors.length > 0 ? errors[0] : 'Unknown error'}`, 'error');
        console.error('Save errors:', errors);
    }
    displayGameLevels(); // Refresh display
}

// Discard all pending changes for a specific level
function discardLevelChanges(levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (!levelChanges || levelChanges.size === 0) {
        showNotification('No pending changes to discard for this level.', 'info');
        return;
    }

    const changeCount = levelChanges.size;

    if (confirm(`Are you sure you want to discard ${changeCount} unsaved changes for this level? This action cannot be undone.`)) {
        // Remove all pending changes for this level
        pendingChanges.delete(levelID);

        showNotification(`Discarded ${changeCount} pending changes.`, 'info');
        displayGameLevels(); // Refresh display to remove pending indicators
    }
}

// Delete question
async function deleteQuestion(questionId) {
    // Find the question and check if deletion is allowed
    let canDelete = false;
    let levelID = null;

    for (const level of gameLevels) {
        const question = level.questions.find(q => q.id === questionId);
        if (question) {
            levelID = level.levelID;
            canDelete = level.canEdit && question.canEdit;
            break;
        }
    }

    if (!canDelete) {
        showNotification('Deletion is disabled for this question', 'error');
        return;
    }

    if (confirm('Are you sure you want to delete this question?')) {
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=delete_content&content_id=${questionId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                showNotification('Question deleted successfully', 'success');

                // Remove the question from the local state
                const level = gameLevels.find(l => l.levelID === levelID);
                if (level) {
                    level.questions = level.questions.filter(q => q.id !== questionId);
                    
                    // Also remove from pending changes if it exists there
                    removePendingQuestion(questionId, levelID);
                }

                // Also remove from original questions to be safe
                originalQuestions.delete(questionId);
                
                // Refresh the display without a full reload
                displayGameLevels();
            } else {
                throw new Error(result.error || 'Failed to delete question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
        }
    }
}

// Close modal
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeGameData);

// Add form submit event listener
const questionForm = document.getElementById('questionForm');
if (questionForm) {
    questionForm.addEventListener('submit', handleAddQuestion);
}
