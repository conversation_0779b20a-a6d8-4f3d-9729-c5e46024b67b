<?php
// Disable error display to prevent HTML output
ini_set('display_errors', 0);
error_reporting(0);

// Set headers first
header('Content-Type: application/json');

try {
    require_once 'dbconnection.php';
    
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Validate level parameter first
    if (!isset($_GET['level']) || !is_numeric($_GET['level'])) {
        throw new Exception("Invalid or missing level parameter");
    }

    $Clevel = $_GET['level'];

    // Prepare and execute the query with parameter binding
    $stmt = $conn->prepare("SELECT *
                           FROM game_content
                           WHERE level_number = ?
                           ORDER BY RAND()");
    $stmt->execute([$Clevel]);
    
    // Fetch all results
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Convert correct_answer to the actual option value for each question
    foreach ($results as &$row) {
        $correct = $row['correct_answer'];
        // If correct_answer is a number (1-4), map to the corresponding option value
        if (is_numeric($correct) && $correct >= 1 && $correct <= 4) {
            $row['correct_answer_value'] = $row['option' . $correct];
        } else {
            // Otherwise, assume it's already the value
            $row['correct_answer_value'] = $correct;
        }
    }
    unset($row);

    // Return the results as JSON
    echo json_encode([
        'success' => true,
        'data' => $results
    ]);
    
} catch(PDOException $e) {
    // Return error message as JSON
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch(Exception $e) {
    // Catch any other errors
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
