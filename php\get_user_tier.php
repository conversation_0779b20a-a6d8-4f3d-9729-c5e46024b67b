<?php
header('Content-Type: application/json');
session_start();

    $pdo = new PDO('mysql:host=localhost;dbname=dbfunconnect', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $response = [
        'success' => false,
        'message' => '',
        'exp' => []
    ];
    
    try {
        // Verify database connection
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
    
        $stmt = $pdo->query("SELECT expID, expName FROM exp ORDER BY expID");
        
        if ($stmt === false) {
            throw new Exception('Query execution failed');
        }
    
        $tiers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
        if (empty($tiers)) {
            $response['message'] = 'EXP table is empty';
        } else {
            $response['success'] = true;
            $response['exp'] = $tiers;
            $response['count'] = count($tiers); // For debugging
        }
    } catch (PDOException $e) {
        $response['message'] = 'Database error: ' . $e->getMessage();
    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }
    
    // Log the response for debugging
    error_log('Tier table response: ' . print_r($response, true));
    echo json_encode($response);