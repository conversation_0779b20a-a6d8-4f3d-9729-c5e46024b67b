<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cable Quest - Login</title>
    <link rel="stylesheet" href="../css/style_login_signup.css">
</head>
<body>
    <div class="header">
        <p class="title"><a href="homepage.html">CABLE QUEST</a></p>
    </div>

    <div class="body">
        <div class="login-cont">
            <p class="login-title">Login</p>

            <form id="login-form" action="../php/login.php" method="post">
                <label for="username"><p>Player name</p></label>
                <input type="text" id="username" name="username" placeholder="" required>
                <label for="password"><p>Password</p></label>
                <input type="password" id="password" name="password" placeholder="" required>
                <div class="error-message" id="login-error"></div>
                <div class="btn-cont">
                    <a href="signup.html"><p>New Player?</p></a>
                    <button type="submit" id="login-button">Login</button>
                </div>
            </form> 
        </div>
    </div>

    <!-- Custom Modal -->
    <div id="custom-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-title">Warning</h5>
            </div>
            <div class="modal-body" id="modal-body">
                Invalid Username or Password!
            </div>
            <div class="modal-footer">
                <button id="modal-ok-button" class="modal-button">OK</button>
            </div>
        </div>
    </div>

    <script src="../js/login.js"></script>
</body>
</html>