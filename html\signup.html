<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cable Quest - Sign Up</title>
    <link rel="stylesheet" href="../css/style_login_signup.css">
</head>
<body>
    <div class="header">
        <p class="title"><a href="homepage.html">CABLE QUEST</a></p>
    </div>

    <div class="body">
        <div class="signup-cont">
            <p class="signup-title">Create Account</p>

            <form action="../php/signup.php" id="signup_form" method="post">
                <label for="username"><p>Player name</p></label>
                <input type="text" id="username" name="username" placeholder="" required>
                <label for="password" id="password_lbl"><p>Password</p></label>
                <input type="password" id="password" name="password" placeholder="" required>
                <label for="confirmpass" id="confirmpass_lbl"><p>Confirm Password</p></label>
                <input type="password" id="confirmpass" name="confirmpass" placeholder="" required>
                <div class="error-message" id="signup-error"></div>
                <div class="btn-cont">
                    <a href="login.html"><p>Already have an account?</p></a>
                    <button type="submit" id="signup">Enter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Custom Modal -->
    <div id="custom-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-title">Account Confirmation</h5>
            </div>
            <div class="modal-body" id="modal-body">
                Are you sure you want to create an account?
            </div>
            <div class="modal-footer">
                <button id="modal-yes-button" class="modal-button primary">Yes</button>
                <button id="modal-no-button" class="modal-button">No</button>
            </div>
        </div>
    </div>

    <script src="../js/signup.js"></script>
</body>
</html>