<?php
require_once 'dbconnection.php';

// Set headers for JSON response
header('Content-Type: application/json');

session_start();
$user_id = $_SESSION['userId'] ?? null;

if (!$user_id) {
    http_response_code(401);
    die(json_encode(['success' => false, 'error' => 'User not authenticated']));
}

try {
    // Initialize database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get all levels from game_content
    $stmt = $conn->prepare("SELECT DISTINCT level_number FROM game_content ORDER BY level_number");
    $stmt->execute();
    $all_levels = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Get user's progress for each level
    $stmt = $conn->prepare("
        SELECT level_number, isUnlocked, levelScore, levelStar 
        FROM user_levels 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    $user_levels = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Create a map of user's levels for easy lookup
    $user_levels_map = [];
    foreach ($user_levels as $level) {
        $user_levels_map[$level['level_number']] = $level;
    }

    // Prepare response with all levels
    $levels = [];
    foreach ($all_levels as $level_number) {
        $level_data = [
            'level_number' => $level_number,
            'isUnlocked' => false,
            'levelScore' => null,
            'levelStar' => null
        ];

        // If user has progress for this level, update the data
        if (isset($user_levels_map[$level_number])) {
            $user_level = $user_levels_map[$level_number];
            $level_data['isUnlocked'] = (bool)$user_level['isUnlocked'];
            $level_data['levelScore'] = $user_level['levelScore'];
            $level_data['levelStar'] = $user_level['levelStar'];
        }

        $levels[] = $level_data;
    }

    echo json_encode([
        'success' => true,
        'levels' => $levels
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} 